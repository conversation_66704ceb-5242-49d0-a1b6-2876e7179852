# Databricks notebook source
# MAGIC %md
# MAGIC # Item - Bronze to Silver Transformation
# MAGIC
# MAGIC This notebook processes item data from the bronze layer to the silver layer. It applies data type conversions, handles timestamp formatting, standardizes values, and performs data quality checks.

# COMMAND ----------

# MAGIC %run ../../start_up

# COMMAND ----------

# MAGIC %run ../../utils/silver_utils

# COMMAND ----------

# MAGIC %run ../../utils/bronze_reader_utils

# COMMAND ----------

from pyspark.sql.functions import (
    input_file_name, current_timestamp, lit, col, to_timestamp, when,
    upper, regexp_extract, datediff, expr, regexp_replace
)
from pyspark.sql.utils import AnalysisException
from delta.tables import DeltaTable

# Use the logger configuration from startup
item_silver_logger = create_logger(component_log_levels=component_log_levels)
item_silver_logger.info("Initializing notebook")

# Extract frequently used config values into variables
catalog = pipeline_config["catalog"]
target_date = pipeline_config["default_processing_date"]
bronze_schema = pipeline_config["schemas"]["bronze"]
silver_schema = pipeline_config["schemas"]["silver"]
bronze_path = pipeline_config["paths"]["bronze_path"]
silver_path = pipeline_config["paths"]["silver_path"]
bronze_format = pipeline_config["file_formats"]["bronze"]
silver_format = pipeline_config["file_formats"]["silver"]
delta_properties = pipeline_config["delta_properties"]

# Switch to catalog
spark.sql(f"USE CATALOG {catalog}")

# COMMAND ----------

# Get item table configuration from table_config
item_config = table_config["tables"]["item"]
item_silver_logger.info(f"Loaded item configuration from table_config")

# Table metadata for item table
table_metadata = {
    "primary_key": item_config["silver"]["primary_key"],
    "column_mapping": item_config["silver"]["column_mapping_bronze_to_silver"],
    "timestamp_columns": ["manufacture_date", "expiry_date", "created_timestamp", "modified_timestamp"],
    "numeric_columns": ["package_size", "shelf_life_days"],
    "boolean_columns": ["is_active"],
    "units_of_measure": ["EA", "KG", "G", "L", "ML", "CM", "M", "PACK", "CASE", "BOX", "PALLET"]
}

# COMMAND ----------

# Function to read data from bronze layer
@log_execution(item_silver_logger)
def read_bronze_data(date=None):
    """Read bronze data using the utility function that handles date folder structure."""
    return read_bronze_data_with_date_structure(
        spark=spark,
        bronze_path=bronze_path,
        table_name="item",
        bronze_format=bronze_format,
        date=date,
        logger=item_silver_logger
    )

# Function to check referential integrity
@log_execution(item_silver_logger)
def check_referential_integrity(df):
    item_silver_logger.info("Checking referential integrity for item data")

    # Check if product_id exists in product table
    if "product_id" in df.columns:
        try:
            # Check if product table exists
            product_table = f"{catalog}.{silver_schema}.product"
            try:
                spark.sql(f"DESCRIBE TABLE {product_table}")
                table_exists = True
            except AnalysisException:
                table_exists = False

            if table_exists:
                # Get distinct product_ids from product table
                product_ids = spark.table(product_table).select("product_id").distinct()

                # Add validation column
                df = df.join(
                    product_ids,
                    df["product_id"] == product_ids["product_id"],
                    "left"
                ).withColumn(
                    "product_id_valid",
                    product_ids["product_id"].isNotNull()
                ).drop(product_ids["product_id"])
            else:
                # If product table doesn't exist yet, skip validation
                item_silver_logger.warning(f"Referenced table {product_table} does not exist. Skipping validation for product_id.")
                df = df.withColumn("product_id_valid", lit(True))
        except Exception as e:
            item_silver_logger.error(f"Error checking referential integrity for product_id: {str(e)}")
            df = df.withColumn("product_id_valid", lit(True))  # Default to valid in case of error

    return df

# Function to apply transformations based on table metadata
@log_execution(item_silver_logger)
def apply_transformations(df):
    item_silver_logger.info("Applying transformations to item data")

    # Apply column mapping from bronze to silver
    column_mapping = table_metadata["column_mapping"]
    df = apply_column_mapping(df, column_mapping)

    # Apply timestamp conversions
    for ts_col in table_metadata["timestamp_columns"]:
        if ts_col in df.columns:
            df = df.withColumn(ts_col, to_timestamp(col(ts_col), "M/d/yyyy"))

    # Apply numeric conversions
    for num_col in table_metadata["numeric_columns"]:
        if num_col in df.columns:
            df = df.withColumn(num_col, col(num_col).cast("double"))

    # Apply boolean conversions
    for bool_col in table_metadata["boolean_columns"]:
        if bool_col in df.columns:
            df = df.withColumn(bool_col, col(bool_col).cast("boolean"))

    # Validate pack_size > 0
    if "pack_size" in df.columns:
        df = df.withColumn(
            "pack_size_valid",
            col("pack_size") > 0
        )

    # Validate shelf_life_days > 0
    if "shelf_life_days" in df.columns:
        df = df.withColumn(
            "shelf_life_valid",
            col("shelf_life_days") > 0
        )

    # Validate manufacture_date < expiry_date
    if "manufacture_date" in df.columns and "expiry_date" in df.columns:
        df = df.withColumn(
            "date_valid",
            col("manufacture_date") < col("expiry_date")
        )

    # Standardize unit_of_measure
    if "unit_of_measure" in df.columns:
        df = df.withColumn("unit_of_measure", upper(col("unit_of_measure")))
        df = df.withColumn(
            "unit_of_measure_valid",
            col("unit_of_measure").isin([unit.upper() for unit in table_metadata["units_of_measure"]])
        )

    # Parse measurement information
    if "measurement" in df.columns:
        # Parse measurement into value and unit (assuming format like "500 g" or "1 L")
        df = df.withColumn(
            "measurement_value",
            regexp_extract(col("measurement"), "^([\\d.]+)", 1).cast("double")
        )

        df = df.withColumn(
            "measurement_unit",
            regexp_extract(col("measurement"), "[\\d.]+\\s+([a-zA-Z]+)", 1)
        )

        # Validate measurement value > 0
        df = df.withColumn(
            "measurement_valid",
            col("measurement_value") > 0
        )

    # Calculate shelf life days if not already present
    if "manufacture_date" in df.columns and "expiry_date" in df.columns and "shelf_life_days" not in df.columns:
        df = df.withColumn(
            "shelf_life_days",
            datediff(col("expiry_date"), col("manufacture_date"))
        )

    # Check referential integrity
    df = check_referential_integrity(df)

    # Add data quality score
    quality_columns = [col for col in df.columns if col.endswith("_valid")]
    if quality_columns:
        # Calculate quality score as percentage of passing validations
        quality_expr = " + ".join([f"CASE WHEN {col} THEN 1 ELSE 0 END" for col in quality_columns])
        df = df.withColumn(
            "data_quality_score",
            expr(f"({quality_expr}) * 100.0 / {len(quality_columns)}")
        )
    else:
        df = df.withColumn("data_quality_score", lit(100.0))

    # Add processing timestamp
    df = df.withColumn("processed_at", current_timestamp())

    return log_dataframe_info(df, "item_transformed", item_silver_logger)

# Function to write data to silver layer
@log_execution(item_silver_logger)
def write_to_silver(df):
    item_silver_logger.info("Writing item data to silver layer")
    table_name = "item"
    target_path = f"{silver_path}/{table_name}"

    # Create or replace the table in Unity Catalog
    try:
        # Check if table exists
        spark.sql(f"DESCRIBE TABLE {catalog}.{silver_schema}.{table_name}")
        table_exists = True
    except AnalysisException:
        table_exists = False

    # Write data using Delta format
    if table_exists:
        # Merge data using primary key
        item_silver_logger.info(f"Merging data into existing table {catalog}.{silver_schema}.{table_name}")
        delta_table = DeltaTable.forName(spark, f"{catalog}.{silver_schema}.{table_name}")

        # Merge condition based on primary key
        merge_condition = f"target.{table_metadata['primary_key']} = source.{table_metadata['primary_key']}"

        # Perform merge operation
        delta_table.alias("target").merge(
            df.alias("source"),
            merge_condition
        ).whenMatchedUpdateAll().whenNotMatchedInsertAll().execute()
    else:
        # Create new table
        item_silver_logger.info(f"Creating new table {catalog}.{silver_schema}.{table_name}")
        df.write \
            .format(silver_format) \
            .option("mergeSchema", "true") \
            .mode("overwrite") \
            .saveAsTable(f"{catalog}.{silver_schema}.{table_name}")

    return df

# Main processing function
@log_execution(item_silver_logger)
def process_bronze_to_silver(target_date=None):
    item_silver_logger.info(f"Processing item data from bronze to silver for date: {target_date if target_date else 'all'}")

    # Read bronze data
    bronze_df = read_bronze_data(target_date)
    if bronze_df is None or bronze_df.isEmpty():
        item_silver_logger.warning(f"No bronze data found for item on date {target_date}")
        return None

    # Apply transformations
    silver_df = apply_transformations(bronze_df)

    # Write to silver layer
    final_df = write_to_silver(silver_df)

    item_silver_logger.info("Completed bronze to silver processing for item")
    return final_df

# COMMAND ----------

# Process item data
silver_df = process_bronze_to_silver(target_date)
if silver_df is not None:
    log_dataframe_info(silver_df, "item_silver_final", item_silver_logger)
    display(silver_df)