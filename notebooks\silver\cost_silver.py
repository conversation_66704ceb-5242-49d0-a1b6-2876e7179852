# Databricks notebook source
# MAGIC %md
# MAGIC # Cost - Bronze to Silver Transformation
# MAGIC
# MAGIC This notebook processes cost data from the bronze layer to the silver layer. It applies data type conversions, handles timestamp formatting, standardizes values, and performs data quality checks.

# COMMAND ----------

# MAGIC %run ../../start_up

# COMMAND ----------

# MAGIC %run ../../utils/silver_utils

# COMMAND ----------

from pyspark.sql.functions import (
    input_file_name, current_timestamp, lit, col, to_timestamp, when,
    upper, regexp_replace, expr
)
from pyspark.sql.utils import AnalysisException
from delta.tables import DeltaTable

# Use the logger configuration from startup
cost_silver_logger = create_logger(log_file=log_file, component_log_levels=component_log_levels)
cost_silver_logger.info("Initializing notebook")

# Extract frequently used config values into variables
catalog = pipeline_config["catalog"]
target_date = pipeline_config["default_processing_date"]
bronze_schema = pipeline_config["schemas"]["bronze"]
silver_schema = pipeline_config["schemas"]["silver"]
bronze_path = pipeline_config["paths"]["bronze_path"]
silver_path = pipeline_config["paths"]["silver_path"]
silver_format = pipeline_config["file_formats"]["silver"]
delta_properties = pipeline_config["delta_properties"]

# Switch to catalog
spark.sql(f"USE CATALOG {catalog}")

# COMMAND ----------

# Get cost table configuration from table_config
cost_config = table_config["tables"]["cost"]
cost_silver_logger.info(f"Loaded cost configuration from table_config")

# Table metadata for cost table
table_metadata = {
    "primary_key": cost_config["silver"]["primary_key"],
    "column_mapping": cost_config["silver"]["column_mapping_bronze_to_silver"],
    "timestamp_columns": ["created_timestamp", "modified_timestamp"],
    "numeric_columns": ["selling_price", "purchase_cost", "margin_percentage"],
    "boolean_columns": ["is_active"],
    "cost_types": ["Regular", "Promotional", "Clearance", "Seasonal", "Wholesale", "Retail"]
}

# COMMAND ----------

# Function to read data from bronze layer
@log_execution(cost_silver_logger)
def read_bronze_data(date=None):
    cost_silver_logger.info(f"Reading bronze data for table: cost, date: {date if date else 'all'}")
    if date:
        # Read specific partition
        df = spark.read.option("mergeSchema", "true").parquet(f"{bronze_path}/cost/ingestion_date={date}")
    else:
        # Read all partitions with schema merging
        df = spark.read.option("mergeSchema", "true").parquet(f"{bronze_path}/cost")

    return log_dataframe_info(df, "cost_bronze", cost_silver_logger)

# Function to check referential integrity
@log_execution(cost_silver_logger)
def check_referential_integrity(df):
    cost_silver_logger.info("Checking referential integrity for cost data")

    # Check if item_id exists in item table
    if "item_id" in df.columns:
        try:
            # Check if item table exists
            item_table = f"{catalog}.{silver_schema}.item"
            try:
                spark.sql(f"DESCRIBE TABLE {item_table}")
                table_exists = True
            except AnalysisException:
                table_exists = False

            if table_exists:
                # Get distinct item_ids from item table
                item_ids = spark.table(item_table).select("item_id").distinct()

                # Add validation column
                df = df.join(
                    item_ids,
                    df["item_id"] == item_ids["item_id"],
                    "left"
                ).withColumn(
                    "item_id_valid",
                    item_ids["item_id"].isNotNull()
                ).drop(item_ids["item_id"])
            else:
                # If item table doesn't exist yet, skip validation
                cost_silver_logger.warning(f"Referenced table {item_table} does not exist. Skipping validation for item_id.")
                df = df.withColumn("item_id_valid", lit(True))
        except Exception as e:
            cost_silver_logger.error(f"Error checking referential integrity for item_id: {str(e)}")
            df = df.withColumn("item_id_valid", lit(True))  # Default to valid in case of error

    return df

# Function to apply transformations based on table metadata
@log_execution(cost_silver_logger)
def apply_transformations(df):
    cost_silver_logger.info("Applying transformations to cost data")

    # Apply column mapping from bronze to silver
    column_mapping = table_metadata["column_mapping"]
    df = apply_column_mapping(df, column_mapping)

    # Apply timestamp conversions
    for ts_col in table_metadata["timestamp_columns"]:
        if ts_col in df.columns:
            df = df.withColumn(ts_col, to_timestamp(col(ts_col), "M/d/yyyy"))

    # Apply numeric conversions
    for num_col in table_metadata["numeric_columns"]:
        if num_col in df.columns:
            df = df.withColumn(num_col, col(num_col).cast("double"))

    # Apply boolean conversions
    for bool_col in table_metadata["boolean_columns"]:
        if bool_col in df.columns:
            df = df.withColumn(bool_col, col(bool_col).cast("boolean"))

    # Validate selling_price >= 0
    if "selling_price" in df.columns:
        df = df.withColumn(
            "selling_price_valid",
            col("selling_price") >= 0
        )

    # Validate purchase_cost >= 0
    if "purchase_cost" in df.columns:
        df = df.withColumn(
            "purchase_cost_valid",
            col("purchase_cost") >= 0
        )

    # Validate margin_percentage is between 0-100
    if "margin_percentage" in df.columns:
        df = df.withColumn(
            "margin_percentage_valid",
            (col("margin_percentage") >= 0) & (col("margin_percentage") <= 100)
        )

    # Validate selling_price >= purchase_cost
    if "selling_price" in df.columns and "purchase_cost" in df.columns:
        df = df.withColumn(
            "cost_relationship_valid",
            col("selling_price") >= col("purchase_cost")
        )

    # Standardize cost_type
    if "cost_type" in df.columns:
        df = df.withColumn("cost_type", upper(col("cost_type")))
        df = df.withColumn(
            "cost_type_valid",
            col("cost_type").isin([type.upper() for type in table_metadata["cost_types"]])
        )

    # Calculate margin_amount if not present
    if "selling_price" in df.columns and "purchase_cost" in df.columns and "margin_amount" not in df.columns:
        df = df.withColumn(
            "margin_amount",
            col("selling_price") - col("purchase_cost")
        )

    # Check referential integrity
    df = check_referential_integrity(df)

    # Add data quality score
    quality_columns = [col for col in df.columns if col.endswith("_valid")]
    if quality_columns:
        # Calculate quality score as percentage of passing validations
        quality_expr = " + ".join([f"CASE WHEN {col} THEN 1 ELSE 0 END" for col in quality_columns])
        df = df.withColumn(
            "data_quality_score",
            expr(f"({quality_expr}) * 100.0 / {len(quality_columns)}")
        )
    else:
        df = df.withColumn("data_quality_score", lit(100.0))

    # Add processing timestamp
    df = df.withColumn("processed_at", current_timestamp())

    return log_dataframe_info(df, "cost_transformed", cost_silver_logger)

# Function to write data to silver layer
@log_execution(cost_silver_logger)
def write_to_silver(df):
    cost_silver_logger.info("Writing cost data to silver layer")
    table_name = "cost"
    target_path = f"{silver_path}/{table_name}"

    # Create or replace the table in Unity Catalog
    try:
        # Check if table exists
        spark.sql(f"DESCRIBE TABLE {catalog}.{silver_schema}.{table_name}")
        table_exists = True
    except AnalysisException:
        table_exists = False

    # Write data using Delta format
    if table_exists:
        # Merge data using primary key
        cost_silver_logger.info(f"Merging data into existing table {catalog}.{silver_schema}.{table_name}")
        delta_table = DeltaTable.forName(spark, f"{catalog}.{silver_schema}.{table_name}")

        # Merge condition based on primary key
        merge_condition = f"target.{table_metadata['primary_key']} = source.{table_metadata['primary_key']}"

        # Perform merge operation
        delta_table.alias("target").merge(
            df.alias("source"),
            merge_condition
        ).whenMatchedUpdateAll().whenNotMatchedInsertAll().execute()
    else:
        # Create new table
        cost_silver_logger.info(f"Creating new table {catalog}.{silver_schema}.{table_name}")
        df.write \
            .format(silver_format) \
            .option("mergeSchema", "true") \
            .mode("overwrite") \
            .saveAsTable(f"{catalog}.{silver_schema}.{table_name}")

    return df

# Main processing function
@log_execution(cost_silver_logger)
def process_bronze_to_silver(target_date=None):
    cost_silver_logger.info(f"Processing cost data from bronze to silver for date: {target_date if target_date else 'all'}")

    # Read bronze data
    bronze_df = read_bronze_data(target_date)
    if bronze_df is None or bronze_df.isEmpty():
        cost_silver_logger.warning(f"No bronze data found for cost on date {target_date}")
        return None

    # Apply transformations
    silver_df = apply_transformations(bronze_df)

    # Write to silver layer
    final_df = write_to_silver(silver_df)

    cost_silver_logger.info("Completed bronze to silver processing for cost")
    return final_df

# COMMAND ----------

# Process cost data
silver_df = process_bronze_to_silver(target_date)
if silver_df is not None:
    log_dataframe_info(silver_df, "cost_silver_final", cost_silver_logger)
    display(silver_df)