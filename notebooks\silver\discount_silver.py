# Databricks notebook source
# MAGIC %md
# MAGIC # Discount - Bronze to Silver Transformation
# MAGIC
# MAGIC This notebook processes discount data from the bronze layer to the silver layer. It applies data type conversions, handles timestamp formatting, standardizes values, and performs data quality checks.

# COMMAND ----------

# MAGIC %run ../../start_up

# COMMAND ----------

from pyspark.sql.functions import (
    input_file_name, current_timestamp, lit, col, to_timestamp, when, 
    upper, regexp_replace, datediff, expr
)
from pyspark.sql.utils import AnalysisException
from delta.tables import DeltaTable

# Use the logger configuration from startup
discount_silver_logger = create_logger(log_file=log_file, component_log_levels=component_log_levels)
discount_silver_logger.info("Initializing notebook")
 
# Extract frequently used config values into variables
catalog = pipeline_config["catalog"]
target_date = pipeline_config["default_processing_date"]
bronze_schema = pipeline_config["schemas"]["bronze"]
silver_schema = pipeline_config["schemas"]["silver"]
bronze_path = pipeline_config["paths"]["bronze_path"]
silver_path = pipeline_config["paths"]["silver_path"]
silver_format = pipeline_config["file_formats"]["silver"]
delta_properties = pipeline_config["delta_properties"]

# Switch to catalog
spark.sql(f"USE CATALOG {catalog}")

# COMMAND ----------

# Table metadata for discount table
table_metadata = {
    "primary_key": "discount_id",
    "timestamp_columns": ["start_date", "end_date", "created_timestamp", "modified_timestamp"],
    "numeric_columns": ["discount_percentage"],
    "discount_types": ["Percentage", "Fixed Amount", "Buy One Get One", "Loyalty", "Seasonal", "Clearance"],
    "discount_statuses": ["Active", "Inactive", "Expired", "Scheduled"]
}

# COMMAND ----------

# Function to read data from bronze layer
@log_execution(discount_silver_logger)
def read_bronze_data(date=None):
    discount_silver_logger.info(f"Reading bronze data for table: discount, date: {date if date else 'all'}") 
    if date:
        # Read specific partition
        df = spark.read.option("mergeSchema", "true").parquet(f"{bronze_path}/discount/ingestion_date={date}")
    else:
        # Read all partitions with schema merging
        df = spark.read.option("mergeSchema", "true").parquet(f"{bronze_path}/discount")
    
    return log_dataframe_info(df, "discount_bronze", discount_silver_logger)

# Function to apply transformations based on table metadata
@log_execution(discount_silver_logger)
def apply_transformations(df):
    discount_silver_logger.info("Applying transformations to discount data")
    
    # Apply timestamp conversions
    for ts_col in table_metadata["timestamp_columns"]:
        if ts_col in df.columns:
            df = df.withColumn(ts_col, to_timestamp(col(ts_col), "M/d/yyyy"))
    
    # Apply numeric conversions
    for num_col in table_metadata["numeric_columns"]:
        if num_col in df.columns:
            df = df.withColumn(num_col, col(num_col).cast("double"))
    
    # Validate discount_percentage is between 0-100
    if "discount_percentage" in df.columns:
        df = df.withColumn(
            "discount_percentage_valid", 
            (col("discount_percentage") >= 0) & (col("discount_percentage") <= 100)
        )
    
    # Validate start_date < end_date
    if "start_date" in df.columns and "end_date" in df.columns:
        df = df.withColumn(
            "date_valid", 
            col("start_date") < col("end_date")
        )
    
    # Standardize discount_type
    if "discount_type" in df.columns:
        df = df.withColumn("discount_type", upper(col("discount_type")))
        df = df.withColumn(
            "discount_type_valid", 
            col("discount_type").isin([type.upper() for type in table_metadata["discount_types"]])
        )
    
    # Standardize discount_status
    if "discount_status" in df.columns:
        df = df.withColumn("discount_status", upper(col("discount_status")))
        df = df.withColumn(
            "discount_status_valid", 
            col("discount_status").isin([status.upper() for status in table_metadata["discount_statuses"]])
        )
    
    # Calculate discount duration in days
    if "start_date" in df.columns and "end_date" in df.columns:
        df = df.withColumn(
            "discount_duration_days",
            datediff(col("end_date"), col("start_date"))
        )
    
    # Flag expired discounts
    if "end_date" in df.columns:
        df = df.withColumn(
            "is_expired",
            current_timestamp() > col("end_date")
        )
    
    # Add data quality score
    quality_columns = [col for col in df.columns if col.endswith("_valid")]
    if quality_columns:
        # Calculate quality score as percentage of passing validations
        quality_expr = " + ".join([f"CASE WHEN {col} THEN 1 ELSE 0 END" for col in quality_columns])
        df = df.withColumn(
            "data_quality_score",
            expr(f"({quality_expr}) * 100.0 / {len(quality_columns)}")
        )
    else:
        df = df.withColumn("data_quality_score", lit(100.0))
    
    # Add processing timestamp
    df = df.withColumn("processed_at", current_timestamp())
    
    return log_dataframe_info(df, "discount_transformed", discount_silver_logger)

# Function to write data to silver layer
@log_execution(discount_silver_logger)
def write_to_silver(df):
    discount_silver_logger.info("Writing discount data to silver layer")
    table_name = "discount"
    target_path = f"{silver_path}/{table_name}"
    
    # Create or replace the table in Unity Catalog
    try:
        # Check if table exists
        spark.sql(f"DESCRIBE TABLE {catalog}.{silver_schema}.{table_name}")
        table_exists = True
    except AnalysisException:
        table_exists = False
    
    # Write data using Delta format
    if table_exists:
        # Merge data using primary key
        discount_silver_logger.info(f"Merging data into existing table {catalog}.{silver_schema}.{table_name}")
        delta_table = DeltaTable.forName(spark, f"{catalog}.{silver_schema}.{table_name}")
        
        # Merge condition based on primary key
        merge_condition = f"target.{table_metadata['primary_key']} = source.{table_metadata['primary_key']}"
        
        # Perform merge operation
        delta_table.alias("target").merge(
            df.alias("source"),
            merge_condition
        ).whenMatchedUpdateAll().whenNotMatchedInsertAll().execute()
    else:
        # Create new table
        discount_silver_logger.info(f"Creating new table {catalog}.{silver_schema}.{table_name}")
        df.write \
            .format(silver_format) \
            .option("mergeSchema", "true") \
            .mode("overwrite") \
            .saveAsTable(f"{catalog}.{silver_schema}.{table_name}")
    
    return df

# Main processing function
@log_execution(discount_silver_logger)
def process_bronze_to_silver(target_date=None):
    discount_silver_logger.info(f"Processing discount data from bronze to silver for date: {target_date if target_date else 'all'}")
    
    # Read bronze data
    bronze_df = read_bronze_data(target_date)
    if bronze_df is None or bronze_df.isEmpty():
        discount_silver_logger.warning(f"No bronze data found for discount on date {target_date}")
        return None
    
    # Apply transformations
    silver_df = apply_transformations(bronze_df)
    
    # Write to silver layer
    final_df = write_to_silver(silver_df)
    
    discount_silver_logger.info("Completed bronze to silver processing for discount")
    return final_df

# COMMAND ----------

# Process discount data
silver_df = process_bronze_to_silver(target_date)
if silver_df is not None:
    log_dataframe_info(silver_df, "discount_silver_final", discount_silver_logger)
    display(silver_df)