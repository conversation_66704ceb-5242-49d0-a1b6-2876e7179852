# Databricks notebook source
# MAGIC %md
# MAGIC # Invoice - Bronze to Silver Transformation
# MAGIC
# MAGIC This notebook processes invoice data from the bronze layer to the silver layer. It applies data type conversions, handles timestamp formatting, standardizes values, and performs data quality checks.

# COMMAND ----------

# MAGIC %run ../../start_up

# COMMAND ----------

# MAGIC %run ../../utils/silver_utils

# COMMAND ----------

# MAGIC %run ../../utils/schema_utils

# COMMAND ----------

from pyspark.sql.functions import (
    input_file_name, current_timestamp, lit, col, to_timestamp, when, 
    upper, regexp_extract, datediff, expr
)
from pyspark.sql.utils import AnalysisException
from delta.tables import DeltaTable

# Use the logger configuration from startup
invoice_silver_logger = create_logger(component_log_levels=component_log_levels)
invoice_silver_logger.info("Initializing notebook")
 
# Extract frequently used config values into variables
catalog = pipeline_config["catalog"]
target_date = pipeline_config["default_processing_date"]
bronze_schema = pipeline_config["schemas"]["bronze"]
silver_schema = pipeline_config["schemas"]["silver"]
bronze_path = pipeline_config["paths"]["bronze_path"]
silver_path = pipeline_config["paths"]["silver_path"]
silver_format = pipeline_config["file_formats"]["silver"]
delta_properties = pipeline_config["delta_properties"]

# Switch to catalog
spark.sql(f"USE CATALOG {catalog}")

# COMMAND ----------

# Get invoice table configuration from table_config
invoice_config = table_config["tables"]["invoice"]
invoice_silver_logger.info(f"Loaded invoice configuration from table_config")

# Table metadata for invoice table
table_metadata = {
    "primary_key": invoice_config["silver"]["primary_key"],
    "column_mapping": invoice_config["silver"]["column_mapping_bronze_to_silver"],
    "timestamp_columns": ["created_timestamp", "modified_timestamp"],
    "status_values": ["Completed", "Pending", "Cancelled", "Refunded"],
    "invoice_types": ["Standard", "Credit", "Debit", "Proforma"],
    "payment_methods": ["Cash", "Credit Card", "Debit Card", "ACH Transfer", "PayPal", "Check", "Wire Transfer"],
    "channel_types": ["In-store", "Online", "Mobile", "Phone", "Mail"]
}

# Log the column mapping for debugging
invoice_silver_logger.info(f"Column mapping: {table_metadata['column_mapping']}")

# COMMAND ----------

# Function to apply transformations based on table metadata
@log_execution(invoice_silver_logger)
def apply_transformations(df):
    invoice_silver_logger.info("Applying transformations to invoice data")
    
    # Apply column mapping from bronze to silver
    column_mapping = table_metadata["column_mapping"]
    df = apply_column_mapping(df, column_mapping)
    
    # Get the schema for invoice table in silver layer
    invoice_schema = get_schema("invoice", "silver")
    
    # Apply schema to enforce data types
    if invoice_schema:
        invoice_silver_logger.info("Applying explicit schema for data type enforcement")
        df = apply_schema(df, invoice_schema)
    else:
        # Fall back to individual conversions if schema is not available
        invoice_silver_logger.info("Falling back to individual data type conversions")
        # Apply timestamp conversions
        df = convert_timestamp_columns(df, table_metadata["timestamp_columns"], "M/d/yyyy")
    
    # Standardize and validate invoice type
    if "invoice_type" in df.columns:
        df = standardize_categorical_values(
            df, 
            "invoice_type", 
            table_metadata["invoice_types"]
        )
    
    # Standardize and validate status
    if "status" in df.columns:
        df = standardize_categorical_values(
            df, 
            "status", 
            table_metadata["status_values"]
        )
    
    # Standardize and validate payment method
    if "payment_method" in df.columns:
        df = standardize_categorical_values(
            df, 
            "payment_method", 
            table_metadata["payment_methods"]
        )
    
    # Standardize and validate channel type
    if "channel_type" in df.columns:
        df = standardize_categorical_values(
            df, 
            "channel_type", 
            table_metadata["channel_types"]
        )
    
    # Validate primary key
    df, pk_valid = validate_primary_key(df, table_metadata["primary_key"])
    if not pk_valid:
        invoice_silver_logger.warning(f"Primary key validation failed for {table_metadata['primary_key']}")
    
    # Calculate data quality score
    df = calculate_data_quality_score(df)
    
    # Add processing timestamp
    df = df.withColumn("processed_at", current_timestamp())
    
    return log_dataframe_info(df, "invoice_transformed", invoice_silver_logger)

# COMMAND ----------

# Main processing function
@log_execution(invoice_silver_logger)
def process_bronze_to_silver(target_date=None):
    invoice_silver_logger.info(f"Processing invoice data from bronze to silver for date: {target_date if target_date else 'all'}")
    
    # Get the schema for invoice table in silver layer
    invoice_schema = get_schema("invoice", "silver")
    invoice_silver_logger.info(f"Using explicit schema for invoice table: {invoice_schema}")
    
    # Read bronze data directly using the utility function
    bronze_df = read_bronze_data(
        spark=spark,
        bronze_path=bronze_path,
        table_name="invoice",
        date=target_date,
        logger=invoice_silver_logger
    )
    
    if bronze_df is None or bronze_df.isEmpty():
        invoice_silver_logger.warning(f"No bronze data found for invoice on date {target_date}")
        return None
    
    # Apply transformations
    silver_df = apply_transformations(bronze_df)
    
    # Write to silver layer directly using the utility function
    final_df = write_silver_data(
        spark=spark,
        df=silver_df,
        silver_path=silver_path,
        table_name="invoice",
        primary_key=table_metadata["primary_key"],
        column_mapping=None,  # Don't apply column mapping again as it's already applied in apply_transformations
        catalog=catalog,
        schema=silver_schema,
        format=silver_format,
        logger=invoice_silver_logger
    )
    
    invoice_silver_logger.info("Completed bronze to silver processing for invoice")
    return final_df

# COMMAND ----------

# Process invoice data
silver_df = process_bronze_to_silver(target_date)
if silver_df is not None:
    log_dataframe_info(silver_df, "invoice_silver_final", invoice_silver_logger)
    display(silver_df)