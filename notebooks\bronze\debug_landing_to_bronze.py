# Databricks notebook source
# MAGIC %md
# MAGIC # Debug Landing to Bronze
# MAGIC
# MAGIC This notebook helps diagnose issues with the landing to bronze process

# COMMAND ----------

# MAGIC %run ../../start_up

# COMMAND ----------

from pyspark.sql.functions import current_date, input_file_name, current_timestamp, lit

# Use the logger configuration from startup
debug_logger = create_logger(component_log_levels=component_log_levels)
debug_logger.info("🔍 Starting debug analysis")

# Extract config values
catalog = pipeline_config["catalog"]
bronze_schema = pipeline_config["schemas"]["bronze"]
landing_path = pipeline_config["paths"]["landing_path"]
bronze_path = pipeline_config["paths"]["bronze_path"]
tables_to_process = pipeline_config["tables_to_process"]

debug_logger.info(f"Config - Catalog: {catalog}, Schema: {bronze_schema}")
debug_logger.info(f"Paths - Landing: {landing_path}, Bronze: {bronze_path}")
debug_logger.info(f"Tables to process: {tables_to_process}")

# COMMAND ----------

# MAGIC %md
# MAGIC ## Step 1: Check Landing Path Access

# COMMAND ----------

try:
    debug_logger.info(f"Checking landing path: {landing_path}")
    files = dbutils.fs.ls(landing_path)
    debug_logger.info(f"Found {len(files)} items in landing path:")
    for file in files:
        debug_logger.info(f"  - {file.name} ({file.size} bytes)")
        print(f"  - {file.name} ({file.size} bytes)")
except Exception as e:
    debug_logger.error(f"Cannot access landing path: {str(e)}")
    print(f"❌ Cannot access landing path: {str(e)}")

# COMMAND ----------

# MAGIC %md
# MAGIC ## Step 2: Check Individual CSV Files (ADLS Structure)

# COMMAND ----------

# Use the target date from config for path construction
target_date = pipeline_config["default_processing_date"]
debug_logger.info(f"Using target date: {target_date}")

# Parse date for ADLS path structure
date_parts = target_date.split('-')
if len(date_parts) == 3:
    year, month, day = date_parts
else:
    # Fallback to 2025/05/31 based on your example
    year, month, day = "2025", "05", "31"

debug_logger.info(f"Date parts: {year}/{month}/{day}")

for table in tables_to_process:
    # ADLS path structure: landing_path/table_name/year/month/day/table_name.csv
    csv_path = f"{landing_path}/{table}/{year}/{month}/{day}/{table}.csv"
    try:
        debug_logger.info(f"Checking file: {csv_path}")
        file_info = dbutils.fs.ls(csv_path)
        debug_logger.info(f"✅ Found {table}.csv ({file_info[0].size} bytes)")
        print(f"✅ Found {table}.csv ({file_info[0].size} bytes)")

        # Try to read first few rows
        df = spark.read.format("csv").option("header", "true").load(csv_path)
        row_count = df.count()
        col_count = len(df.columns)
        debug_logger.info(f"  📊 {row_count} rows, {col_count} columns")
        print(f"  📊 {row_count} rows, {col_count} columns")
        print(f"  📋 Columns: {df.columns}")

    except Exception as e:
        debug_logger.error(f"❌ Issue with {table}.csv: {str(e)}")
        print(f"❌ Issue with {table}.csv: {str(e)}")

        # Try to check if the table directory exists
        table_dir = f"{landing_path}/{table}"
        try:
            table_contents = dbutils.fs.ls(table_dir)
            print(f"  📁 Contents of {table}/ directory:")
            for item in table_contents:
                print(f"    - {item.name}")
        except:
            print(f"  ❌ Table directory {table}/ not found")

# COMMAND ----------

# MAGIC %md
# MAGIC ## Step 3: Check Bronze Path Access

# COMMAND ----------

try:
    debug_logger.info(f"Checking bronze path: {bronze_path}")
    # Try to create a test directory
    test_path = f"{bronze_path}/test_write"
    dbutils.fs.mkdirs(test_path)
    debug_logger.info("✅ Bronze path is writable")
    print("✅ Bronze path is writable")
    
    # Clean up test directory
    dbutils.fs.rm(test_path, True)
    
except Exception as e:
    debug_logger.error(f"❌ Cannot write to bronze path: {str(e)}")
    print(f"❌ Cannot write to bronze path: {str(e)}")

# COMMAND ----------

# MAGIC %md
# MAGIC ## Step 4: Check Unity Catalog Access

# COMMAND ----------

try:
    # Check if catalog exists
    spark.sql(f"USE CATALOG {catalog}")
    debug_logger.info(f"✅ Catalog {catalog} accessible")
    print(f"✅ Catalog {catalog} accessible")
    
    # Check if schema exists
    spark.sql(f"USE SCHEMA {bronze_schema}")
    debug_logger.info(f"✅ Schema {bronze_schema} accessible")
    print(f"✅ Schema {bronze_schema} accessible")
    
    # List existing tables
    tables = spark.sql(f"SHOW TABLES IN {catalog}.{bronze_schema}").collect()
    debug_logger.info(f"📋 Existing tables in {catalog}.{bronze_schema}:")
    print(f"📋 Existing tables in {catalog}.{bronze_schema}:")
    for table in tables:
        debug_logger.info(f"  - {table.tableName}")
        print(f"  - {table.tableName}")
        
except Exception as e:
    debug_logger.error(f"❌ Unity Catalog issue: {str(e)}")
    print(f"❌ Unity Catalog issue: {str(e)}")

# COMMAND ----------

# MAGIC %md
# MAGIC ## Step 5: Test Single Table Processing

# COMMAND ----------

# Test with the first table
test_table = tables_to_process[0]
debug_logger.info(f"🧪 Testing single table processing: {test_table}")
print(f"🧪 Testing single table processing: {test_table}")

try:
    # Read the CSV using ADLS structure
    csv_path = f"{landing_path}/{test_table}/{year}/{month}/{day}/{test_table}.csv"
    df = (
        spark.read.format("csv")
        .option("header", "true")
        .option("inferSchema", "false")
        .load(csv_path)
        .withColumn("source_file_path", input_file_name())
        .withColumn("ingestion_timestamp", current_timestamp())
        .withColumn("ingestion_date", lit("2024-01-15"))
    )
    
    debug_logger.info(f"✅ Successfully read {test_table}")
    print(f"✅ Successfully read {test_table}")
    print(f"📊 Shape: {df.count()} rows, {len(df.columns)} columns")
    
    # Show sample data
    print("📋 Sample data:")
    df.show(5, truncate=False)
    
    # Try to write to bronze
    table_name = f"{catalog}.{bronze_schema}.{test_table}_test"
    df.write.format("delta").mode("overwrite").saveAsTable(table_name)
    
    debug_logger.info(f"✅ Successfully wrote to {table_name}")
    print(f"✅ Successfully wrote to {table_name}")
    
    # Clean up test table
    spark.sql(f"DROP TABLE IF EXISTS {table_name}")
    
except Exception as e:
    debug_logger.error(f"❌ Test failed: {str(e)}")
    print(f"❌ Test failed: {str(e)}")
    import traceback
    traceback.print_exc()

# COMMAND ----------

debug_logger.info("🏁 Debug analysis complete")
print("🏁 Debug analysis complete")
