# Databricks notebook source
# MAGIC %md
# MAGIC # Invoice Line - Bronze to Silver Transformation
# MAGIC
# MAGIC This notebook processes invoice line data from the bronze layer to the silver layer. It applies data type conversions, handles timestamp formatting, standardizes values, and performs data quality checks.

# COMMAND ----------

# MAGIC %run ../../start_up

# COMMAND ----------

# MAGIC %run ../../utils/silver_utils

# COMMAND ----------

# MAGIC %run ../../utils/schema_utils

# COMMAND ----------

from pyspark.sql.functions import (
    input_file_name, current_timestamp, lit, col, to_timestamp, when,
    upper, regexp_extract, datediff, expr, year, month, quarter, dayofmonth, abs
)
from pyspark.sql.utils import AnalysisException
from delta.tables import DeltaTable

# Use the logger configuration from startup with log_path from config
invoice_line_silver_logger = create_logger(component_log_levels=component_log_levels)
invoice_line_silver_logger.info("Initializing notebook")

# Extract frequently used config values into variables
catalog = pipeline_config["catalog"]
target_date = pipeline_config["default_processing_date"]
bronze_schema = pipeline_config["schemas"]["bronze"]
silver_schema = pipeline_config["schemas"]["silver"]
bronze_path = pipeline_config["paths"]["bronze_path"]
silver_path = pipeline_config["paths"]["silver_path"]
silver_format = pipeline_config["file_formats"]["silver"]
delta_properties = pipeline_config["delta_properties"]

# Switch to catalog
spark.sql(f"USE CATALOG {catalog}")

# COMMAND ----------

# Get invoice_line table configuration from table_config
invoice_line_config = table_config["tables"]["invoice_line"]
invoice_line_silver_logger.info(f"Loaded invoice_line configuration from table_config")

# Table metadata for invoice_line table
table_metadata = {
    "primary_key": invoice_line_config["silver"]["primary_key"],
    "column_mapping": invoice_line_config["silver"]["column_mapping_bronze_to_silver"],
    "timestamp_columns": ["invoice_date", "created_timestamp", "modified_timestamp"],
    "numeric_columns": ["quantity", "unit_price", "discount_amount", "tax_amount", "line_total_amount"],
    "foreign_keys": {
        "invoice_id": {"table": "invoice", "column": "invoice_id"},
        "discount_id": {"table": "discount", "column": "discount_id"},
        "item_id": {"table": "item", "column": "item_id"},
        "tax_id": {"table": "tax", "column": "tax_id"},
        "customer_id": {"table": "customer", "column": "customer_id"}
    }
}

# Log the column mapping for debugging
invoice_line_silver_logger.info(f"Column mapping: {table_metadata['column_mapping']}")

# COMMAND ----------

# Function to apply transformations based on table metadata
@log_execution(invoice_line_silver_logger)
def apply_transformations(df):
    invoice_line_silver_logger.info("Applying transformations to invoice_line data")

    # Apply column mapping from bronze to silver
    column_mapping = table_metadata["column_mapping"]
    df = apply_column_mapping(df, column_mapping)

    # Get the schema for invoice_line table in silver layer
    invoice_line_schema = get_schema("invoice_line", "silver")

    # Apply schema to enforce data types
    if invoice_line_schema:
        invoice_line_silver_logger.info("Applying explicit schema for data type enforcement")
        df = apply_schema(df, invoice_line_schema)
    else:
        # Fall back to individual conversions if schema is not available
        invoice_line_silver_logger.info("Falling back to individual data type conversions")
        # Apply timestamp conversions
        df = convert_timestamp_columns(df, table_metadata["timestamp_columns"], "M/d/yyyy")

        # Apply numeric conversions
        df = convert_numeric_columns(df, table_metadata["numeric_columns"])

    # Validate quantity > 0
    if "quantity" in df.columns:
        df = df.withColumn("quantity_valid", col("quantity") > 0)

    # Validate unit_price >= 0
    if "unit_price" in df.columns:
        df = df.withColumn("unit_price_valid", col("unit_price") >= 0)

    # Calculate net_amount
    if "quantity" in df.columns and "unit_price" in df.columns:
        df = df.withColumn("net_amount", col("quantity") * col("unit_price"))

    # Verify line_total_amount calculation
    if all(col in df.columns for col in ["quantity", "unit_price", "discount_amount", "tax_amount", "line_total_amount"]):
        df = df.withColumn(
            "calculated_total",
            (col("quantity") * col("unit_price")) - col("discount_amount") + col("tax_amount")
        )
        df = df.withColumn(
            "total_amount_valid",
            abs(col("calculated_total") - col("line_total_amount")) < 0.01
        )

    # Extract date components for analytics
    if "invoice_date" in df.columns:
        df = df.withColumn("invoice_year", year(col("invoice_date")))
        df = df.withColumn("invoice_month", month(col("invoice_date")))
        df = df.withColumn("invoice_quarter", quarter(col("invoice_date")))
        df = df.withColumn("invoice_day", dayofmonth(col("invoice_date")))

    # Check referential integrity for foreign keys
    for fk_column, reference in table_metadata["foreign_keys"].items():
        if fk_column in df.columns:
            try:
                # Check if referenced table exists
                ref_table = f"{catalog}.{silver_schema}.{reference['table']}"
                try:
                    spark.sql(f"DESCRIBE TABLE {ref_table}")
                    table_exists = True
                except AnalysisException:
                    table_exists = False

                if table_exists:
                    # Get distinct values from referenced table
                    ref_values = spark.table(ref_table).select(reference['column']).distinct()

                    # Add validation column
                    validation_column = f"{fk_column}_valid"
                    df = df.join(
                        ref_values,
                        df[fk_column] == ref_values[reference['column']],
                        "left"
                    ).withColumn(
                        validation_column,
                        col(reference['column']).isNotNull()
                    ).drop(reference['column'])
                else:
                    # If referenced table doesn't exist yet, skip validation
                    invoice_line_silver_logger.warning(f"Referenced table {ref_table} does not exist. Skipping validation for {fk_column}.")
                    df = df.withColumn(f"{fk_column}_valid", lit(True))
            except Exception as e:
                invoice_line_silver_logger.error(f"Error checking referential integrity for {fk_column}: {str(e)}")
                df = df.withColumn(f"{fk_column}_valid", lit(True))  # Default to valid in case of error

    # Validate primary key
    df, pk_valid = validate_primary_key(df, table_metadata["primary_key"])
    if not pk_valid:
        invoice_line_silver_logger.warning(f"Primary key validation failed for {table_metadata['primary_key']}")

    # Calculate data quality score
    df = calculate_data_quality_score(df)

    # Add processing timestamp
    df = df.withColumn("processed_at", current_timestamp())

    return log_dataframe_info(df, "invoice_line_transformed", invoice_line_silver_logger)

# COMMAND ----------

# Main processing function
@log_execution(invoice_line_silver_logger)
def process_bronze_to_silver(target_date=None):
    invoice_line_silver_logger.info(f"Processing invoice_line data from bronze to silver for date: {target_date if target_date else 'all'}")

    # Get the schema for invoice_line table in silver layer
    invoice_line_schema = get_schema("invoice_line", "silver")
    invoice_line_silver_logger.info(f"Using explicit schema for invoice_line table: {invoice_line_schema}")

    # Read bronze data directly using the utility function
    bronze_df = read_bronze_data(
        spark=spark,
        bronze_path=bronze_path,
        table_name="invoice_line",
        date=target_date,
        logger=invoice_line_silver_logger
    )

    if bronze_df is None or bronze_df.isEmpty():
        invoice_line_silver_logger.warning(f"No bronze data found for invoice_line on date {target_date}")
        return None

    # Apply transformations
    silver_df = apply_transformations(bronze_df)

    # Write to silver layer directly using the utility function
    final_df = write_silver_data(
        spark=spark,
        df=silver_df,
        silver_path=silver_path,
        table_name="invoice_line_cleaned",
        primary_key=table_metadata["primary_key"],
        column_mapping=None,  # Don't apply column mapping again as it's already applied in apply_transformations
        catalog=catalog,
        schema=silver_schema,
        format=silver_format,
        logger=invoice_line_silver_logger
    )

    invoice_line_silver_logger.info("Completed bronze to silver processing for invoice_line_cleaned")
    return final_df

# COMMAND ----------

# Process invoice_line data
silver_df = process_bronze_to_silver(target_date)
if silver_df is not None:
    log_dataframe_info(silver_df, "invoice_line_silver_final", invoice_line_silver_logger)
    display(silver_df)