# Databricks notebook source
# MAGIC %md
# MAGIC # Invoice Line - Bronze to Silver Transformation
# MAGIC
# MAGIC This notebook processes invoice line data from the bronze layer to the silver layer. It applies data type conversions, handles timestamp formatting, standardizes values, and performs data quality checks.

# COMMAND ----------

# MAGIC %run ../../start_up

# COMMAND ----------

# MAGIC %run ../../utils/silver_utils

# COMMAND ----------

# MAGIC %run ../../utils/bronze_reader_utils

# COMMAND ----------

from pyspark.sql.functions import (
    input_file_name, current_timestamp, lit, col, to_timestamp, when,
    upper, regexp_extract, datediff, expr, year, month, quarter, dayofmonth, abs
)
from pyspark.sql.utils import AnalysisException
from delta.tables import DeltaTable

# Use the logger configuration from startup with log_path from config
invoice_line_silver_logger = create_logger(component_log_levels=component_log_levels)
invoice_line_silver_logger.info("Initializing notebook")

# Extract frequently used config values into variables
catalog = pipeline_config["catalog"]
target_date = pipeline_config["default_processing_date"]
bronze_schema = pipeline_config["schemas"]["bronze"]
silver_schema = pipeline_config["schemas"]["silver"]
bronze_path = pipeline_config["paths"]["bronze_path"]
silver_path = pipeline_config["paths"]["silver_path"]
bronze_format = pipeline_config["file_formats"]["bronze"]
silver_format = pipeline_config["file_formats"]["silver"]
delta_properties = pipeline_config["delta_properties"]

# Switch to catalog
spark.sql(f"USE CATALOG {catalog}")

# COMMAND ----------

# Get invoice_line table configuration from table_config
invoice_line_config = table_config["tables"]["invoice_line"]
invoice_line_silver_logger.info(f"Loaded invoice_line configuration from table_config")

# Table metadata for invoice_line table
table_metadata = {
    "primary_key": invoice_line_config["silver"]["primary_key"],
    "column_mapping": invoice_line_config["silver"]["column_mapping_bronze_to_silver"],
    "timestamp_columns": ["invoice_date", "created_timestamp", "modified_timestamp"],
    "numeric_columns": ["quantity", "unit_price", "discount_amount", "tax_amount", "line_total_amount"],
    "foreign_keys": {
        "invoice_id": {"table": "invoice", "column": "invoice_id"},
        "discount_id": {"table": "discount", "column": "discount_id"},
        "item_id": {"table": "item", "column": "item_id"},
        "tax_id": {"table": "tax", "column": "tax_id"},
        "customer_id": {"table": "customer", "column": "customer_id"}
    }
}

# Log the column mapping for debugging
invoice_line_silver_logger.info(f"Column mapping: {table_metadata['column_mapping']}")

# COMMAND ----------

# Function to read data from bronze layer
def read_bronze_data(date=None):
    """Read bronze data using the utility function that handles date folder structure."""
    return read_bronze_data_with_date_structure(
        spark=spark,
        bronze_path=bronze_path,
        table_name="invoice_line",
        bronze_format=bronze_format,
        date=date,
        logger=invoice_line_silver_logger
    )

# Function to apply transformations based on table metadata
def apply_transformations(df):
    invoice_line_silver_logger.info("Applying transformations to invoice_line data")

    # Apply column mapping from bronze to silver (simple renaming)
    column_mapping = table_metadata["column_mapping"]
    for old_col, new_col in column_mapping.items():
        if old_col in df.columns:
            df = df.withColumnRenamed(old_col, new_col)

    # Apply timestamp conversions
    for ts_col in table_metadata["timestamp_columns"]:
        if ts_col in df.columns:
            df = df.withColumn(ts_col, to_timestamp(col(ts_col), "M/d/yyyy"))

    # Apply numeric conversions
    for num_col in table_metadata["numeric_columns"]:
        if num_col in df.columns:
            df = df.withColumn(num_col, col(num_col).cast("double"))

    # Validate quantity > 0
    if "quantity" in df.columns:
        df = df.withColumn("quantity_valid", col("quantity") > 0)

    # Validate unit_price >= 0
    if "unit_price" in df.columns:
        df = df.withColumn("unit_price_valid", col("unit_price") >= 0)

    # Calculate net_amount
    if "quantity" in df.columns and "unit_price" in df.columns:
        df = df.withColumn("net_amount", col("quantity") * col("unit_price"))

    # Verify line_total_amount calculation
    if all(col in df.columns for col in ["quantity", "unit_price", "discount_amount", "tax_amount", "line_total_amount"]):
        df = df.withColumn(
            "calculated_total",
            (col("quantity") * col("unit_price")) - col("discount_amount") + col("tax_amount")
        )
        df = df.withColumn(
            "total_amount_valid",
            abs(col("calculated_total") - col("line_total_amount")) < 0.01
        )

    # Extract date components for analytics
    if "invoice_date" in df.columns:
        df = df.withColumn("invoice_year", year(col("invoice_date")))
        df = df.withColumn("invoice_month", month(col("invoice_date")))
        df = df.withColumn("invoice_quarter", quarter(col("invoice_date")))
        df = df.withColumn("invoice_day", dayofmonth(col("invoice_date")))

    # Simple validation for foreign keys (just check not null)
    for fk_column in table_metadata["foreign_keys"].keys():
        if fk_column in df.columns:
            df = df.withColumn(f"{fk_column}_valid", col(fk_column).isNotNull())

    # Simple primary key validation (check not null)
    pk_column = table_metadata["primary_key"]
    if pk_column in df.columns:
        df = df.withColumn(f"{pk_column}_valid", col(pk_column).isNotNull())

    # Calculate simple data quality score
    quality_columns = [col for col in df.columns if col.endswith("_valid")]
    if quality_columns:
        # Calculate quality score as percentage of passing validations
        quality_expr = " + ".join([f"CASE WHEN {col} THEN 1 ELSE 0 END" for col in quality_columns])
        df = df.withColumn(
            "data_quality_score",
            expr(f"({quality_expr}) * 100.0 / {len(quality_columns)}")
        )
    else:
        df = df.withColumn("data_quality_score", lit(100.0))

    # Add processing timestamp
    df = df.withColumn("processed_at", current_timestamp())

    invoice_line_silver_logger.info(f"Transformation complete. DataFrame has {df.count()} rows and {len(df.columns)} columns")
    return df

# COMMAND ----------

# Function to write data to silver layer
def write_to_silver(df):
    invoice_line_silver_logger.info("Writing invoice_line data to silver layer")
    table_name = "invoice_line_cleaned"
    target_path = f"{silver_path}/{table_name}"

    # Create or replace the table in Unity Catalog
    try:
        # Check if table exists
        spark.sql(f"DESCRIBE TABLE {catalog}.{silver_schema}.{table_name}")
        table_exists = True
    except AnalysisException:
        table_exists = False

    # Write data using Delta format
    if table_exists:
        # Merge data using primary key
        invoice_line_silver_logger.info(f"Merging data into existing table {catalog}.{silver_schema}.{table_name}")
        delta_table = DeltaTable.forName(spark, f"{catalog}.{silver_schema}.{table_name}")

        # Merge condition based on primary key
        merge_condition = f"target.{table_metadata['primary_key']} = source.{table_metadata['primary_key']}"

        # Perform merge operation
        delta_table.alias("target").merge(
            df.alias("source"),
            merge_condition
        ).whenMatchedUpdateAll().whenNotMatchedInsertAll().execute()
    else:
        # Create new table
        invoice_line_silver_logger.info(f"Creating new table {catalog}.{silver_schema}.{table_name}")
        df.write \
            .format(silver_format) \
            .option("mergeSchema", "true") \
            .option("path", target_path) \
            .mode("overwrite") \
            .saveAsTable(f"{catalog}.{silver_schema}.{table_name}")

    return df

# Main processing function
def process_bronze_to_silver(target_date=None):
    invoice_line_silver_logger.info(f"Processing invoice_line data from bronze to silver for date: {target_date if target_date else 'all'}")

    # Read bronze data
    bronze_df = read_bronze_data(target_date)
    if bronze_df is None or bronze_df.isEmpty():
        invoice_line_silver_logger.warning(f"No bronze data found for invoice_line on date {target_date}")
        return None

    # Apply transformations
    silver_df = apply_transformations(bronze_df)

    # Write to silver layer
    final_df = write_to_silver(silver_df)

    invoice_line_silver_logger.info("Completed bronze to silver processing for invoice_line_cleaned")
    return final_df

# COMMAND ----------

# Process invoice_line data
silver_df = process_bronze_to_silver(target_date)
if silver_df is not None:
    invoice_line_silver_logger.info(f"Final result: {silver_df.count()} rows, {len(silver_df.columns)} columns")
    silver_df.show(5)