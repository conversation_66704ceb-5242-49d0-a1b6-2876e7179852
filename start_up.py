# Databricks notebook source
# MAGIC %run ./config/config

# COMMAND ----------

# MAGIC %run ./config/table_config

# COMMAND ----------

# MAGIC %run ./utils/logger

# COMMAND ----------

# Define logging configuration to be used across all notebooks
# These variables will be available to other notebooks that run this notebook
# Use log_path from config.ipynb instead of hardcoding the log file path
component_log_levels = {
    "data_loading": "DEBUG",  # More detailed logging for data loading operations
    "transformation": "INFO",  # Standard logging for transformations
    "data_writing": "INFO"     # Standard logging for data writing operations
}

# Create a logger for this notebook using log_path from config
startup_logger = create_logger(log_level="INFO", component_log_levels=component_log_levels)
startup_logger.info("Initializing notebook")
startup_logger.info(f"Log path from config: {log_path}")

# COMMAND ----------

# MAGIC %sql
# MAGIC CREATE SCHEMA IF NOT EXISTS sobeys_uc.bronze;
# MAGIC CREATE SCHEMA IF NOT EXISTS sobeys_uc.silver;
# MAGIC CREATE SCHEMA IF NOT EXISTS sobeys_uc.gold;