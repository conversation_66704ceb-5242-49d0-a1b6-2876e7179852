# Databricks notebook source
# MAGIC %md
# MAGIC # Test Product Pricing Optimization Gold Table
# MAGIC
# MAGIC This notebook tests the product pricing optimization gold table creation and validates the data.

# COMMAND ----------

# MAGIC %run ../../start_up

# COMMAND ----------

from pyspark.sql.functions import col, count, avg, min as spark_min, max as spark_max

# Use the logger configuration from startup
test_logger = create_logger(component_log_levels=component_log_levels)
test_logger.info("🧪 Starting pricing optimization table test")

# Extract config values
catalog = pipeline_config["catalog"]
silver_schema = pipeline_config["schemas"]["silver"]
gold_schema = pipeline_config["schemas"]["gold"]

# COMMAND ----------

# MAGIC %md
# MAGIC ## Step 1: Check Silver Tables Availability

# COMMAND ----------

print("🔍 CHECKING SILVER TABLES FOR PRICING OPTIMIZATION")
print("=" * 60)

required_tables = ["cost", "item", "product", "invoice_line_cleaned"]
table_status = {}

for table_name in required_tables:
    try:
        df = spark.table(f"{catalog}.{silver_schema}.{table_name}")
        row_count = df.count()
        table_status[table_name] = {"status": "✅ Available", "rows": row_count}
        print(f"{table_name}: ✅ {row_count:,} rows")
    except Exception as e:
        table_status[table_name] = {"status": "❌ Missing", "error": str(e)}
        print(f"{table_name}: ❌ Error - {str(e)}")

print(f"\nTable Status Summary: {sum(1 for t in table_status.values() if '✅' in t['status'])}/{len(required_tables)} tables available")

# COMMAND ----------

# MAGIC %md
# MAGIC ## Step 2: Test Data Joins

# COMMAND ----------

print("\n🔗 TESTING DATA JOINS FOR PRICING OPTIMIZATION")
print("=" * 60)

try:
    # Read silver tables
    cost_df = spark.table(f"{catalog}.{silver_schema}.cost")
    item_df = spark.table(f"{catalog}.{silver_schema}.item")
    product_df = spark.table(f"{catalog}.{silver_schema}.product")
    invoice_line_df = spark.table(f"{catalog}.{silver_schema}.invoice_line_cleaned")
    
    print("📊 Silver table row counts:")
    print(f"  Cost: {cost_df.count():,} rows")
    print(f"  Item: {item_df.count():,} rows") 
    print(f"  Product: {product_df.count():,} rows")
    print(f"  Invoice Line: {invoice_line_df.count():,} rows")
    
    # Test joins
    print("\n🔗 Testing joins:")
    
    # Cost + Item join
    cost_item = cost_df.join(item_df.select("item_code", "product_code"), "item_code", "inner")
    print(f"  Cost ⟷ Item: {cost_item.count():,} rows")
    
    # Invoice Line + Item join  
    sales_item = invoice_line_df.join(item_df.select("item_code", "product_code"), "item_code", "inner")
    print(f"  Invoice Line ⟷ Item: {sales_item.count():,} rows")
    
    # Product join
    final_join = cost_item.join(product_df.select("product_code", "name"), "product_code", "inner")
    print(f"  Final join with Product: {final_join.count():,} rows")
    
    if final_join.count() > 0:
        print("✅ All joins successful - data is available for pricing optimization")
    else:
        print("❌ No data after joins - check data relationships")
        
except Exception as e:
    print(f"❌ Error testing joins: {str(e)}")

# COMMAND ----------

# MAGIC %md
# MAGIC ## Step 3: Sample Pricing Calculation

# COMMAND ----------

print("\n💰 SAMPLE PRICING CALCULATION")
print("=" * 60)

try:
    # Simple pricing calculation for top 10 products
    sample_pricing = cost_df.join(
        item_df.select("item_code", "product_code"), "item_code", "inner"
    ).join(
        product_df.select("product_code", col("name").alias("product_name")), "product_code", "inner"
    ).groupBy("product_code", "product_name").agg(
        avg("selling_price").alias("avg_selling_price"),
        avg("purchase_cost").alias("avg_purchase_cost"),
        avg("margin_amount").alias("avg_margin_amount"),
        avg("margin_percentage").alias("avg_margin_percentage")
    ).limit(10)
    
    print("📈 Sample pricing data (top 10 products):")
    sample_pricing.show(10, truncate=False)
    
    # Check for actual sales data
    sales_sample = invoice_line_df.join(
        item_df.select("item_code", "product_code"), "item_code", "inner"
    ).join(
        product_df.select("product_code", col("name").alias("product_name")), "product_code", "inner"
    ).groupBy("product_code", "product_name").agg(
        avg("price").alias("avg_sales_price"),
        count("*").alias("transaction_count")
    ).limit(10)
    
    print("\n💵 Sample sales data (top 10 products):")
    sales_sample.show(10, truncate=False)
    
except Exception as e:
    print(f"❌ Error in sample calculation: {str(e)}")

# COMMAND ----------

# MAGIC %md
# MAGIC ## Step 4: Run Pricing Optimization Table Creation

# COMMAND ----------

print("\n🚀 RUNNING PRICING OPTIMIZATION TABLE CREATION")
print("=" * 60)

try:
    # Run the pricing optimization notebook
    %run ./product_pricing_optimization
    
    print("✅ Pricing optimization table creation completed")
    
except Exception as e:
    print(f"❌ Error running pricing optimization: {str(e)}")

# COMMAND ----------

# MAGIC %md
# MAGIC ## Step 5: Validate Results

# COMMAND ----------

print("\n✅ VALIDATING PRICING OPTIMIZATION RESULTS")
print("=" * 60)

try:
    # Check if gold table was created
    pricing_table = spark.table(f"{catalog}.{gold_schema}.product_pricing_optimization")
    
    print(f"📊 Gold table created successfully:")
    print(f"  Rows: {pricing_table.count():,}")
    print(f"  Columns: {len(pricing_table.columns)}")
    print(f"  Column names: {pricing_table.columns}")
    
    # Show data quality metrics
    print(f"\n📈 Data Quality Metrics:")
    pricing_table.select(
        count("*").alias("total_products"),
        count("current_selling_price").alias("products_with_selling_price"),
        count("actual_avg_sales_price").alias("products_with_sales_data"),
        avg("price_variance").alias("avg_price_variance"),
        spark_min("price_variance").alias("min_price_variance"),
        spark_max("price_variance").alias("max_price_variance")
    ).show()
    
    # Show sample results
    print(f"\n📋 Sample Results:")
    pricing_table.select(
        "product_name", "category", "current_selling_price", 
        "actual_avg_sales_price", "price_variance"
    ).show(5, truncate=False)
    
    print("🎉 Pricing optimization table validation completed successfully!")
    
except Exception as e:
    print(f"❌ Error validating results: {str(e)}")

# COMMAND ----------

test_logger.info("🏁 Pricing optimization table test completed")
