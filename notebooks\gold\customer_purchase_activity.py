# Databricks notebook source
# MAGIC %md
# MAGIC # Aggregated Customer Purchase Activity - Gold Layer Materialized View
# MAGIC
# MAGIC This notebook creates an aggregated materialized view in the gold layer that identifies customers who have made purchases in the last quarter. It uses data from the silver.customer_cleaned and silver.invoice_line tables.

# COMMAND ----------

# MAGIC %run ../../start_up

# COMMAND ----------

from pyspark.sql.functions import (
    col, current_timestamp, lit, datediff, date_sub, max, when, count, sum as sql_sum, 
    avg, min, max, expr, quarter, year, month, dayofmonth, to_date, concat
)
from pyspark.sql.utils import AnalysisException
from delta.tables import DeltaTable

# Use the logger configuration from startup with log_path from config
customer_activity_logger = create_logger(component_log_levels=component_log_levels)
customer_activity_logger.info("Initializing notebook")
 
# Extract frequently used config values into variables
catalog = pipeline_config["catalog"]
silver_schema = pipeline_config["schemas"]["silver"]
gold_schema = pipeline_config["schemas"]["gold"]
gold_path = pipeline_config["paths"]["gold_path"]
gold_format = pipeline_config["file_formats"]["gold"]
materialized_view_properties = pipeline_config["materialized_view_properties"]

# Switch to catalog
spark.sql(f"USE CATALOG {catalog}")

# COMMAND ----------

# Function to read data from silver layer
@log_execution(customer_activity_logger)
def read_silver_data():
    customer_activity_logger.info("Reading customer and invoice_line data from silver layer")
    
    # Read customer data
    try:
        customer_df = spark.table(f"{catalog}.{silver_schema}.customer_cleaned")
        customer_activity_logger.info(f"Successfully read customer data with {customer_df.count()} rows")
    except AnalysisException as e:
        customer_activity_logger.error(f"Error reading customer data: {str(e)}")
        return None, None
    
    # Read invoice_line data
    try:
        invoice_line_df = spark.table(f"{catalog}.{silver_schema}.invoice_line_cleaned")
        customer_activity_logger.info(f"Successfully read invoice_line data with {invoice_line_df.count()} rows")
    except AnalysisException as e:
        customer_activity_logger.error(f"Error reading invoice_line data: {str(e)}")
        return customer_df, None
    
    return customer_df, invoice_line_df

# COMMAND ----------

# Function to create customer purchase activity view
@log_execution(customer_activity_logger)
def create_customer_purchase_activity(customer_df, invoice_line_df):
    customer_activity_logger.info("Creating customer purchase activity view")
    
    if customer_df is None or invoice_line_df is None:
        customer_activity_logger.error("Missing required data for customer purchase activity view")
        return None
    
    # Calculate current date and the start of the previous calendar quarter
    current_date = expr("current_date()")
    
    # Get the current year and quarter
    current_year = year(current_date)
    current_quarter = quarter(current_date)
    
    # Calculate the previous quarter and its year
    prev_quarter = when(current_quarter > 1, current_quarter - 1).otherwise(4)
    prev_quarter_year = when(current_quarter > 1, current_year).otherwise(current_year - 1)
    
    # Calculate the start date of the previous quarter
    last_quarter_start = (
        when(prev_quarter == 1, to_date(concat(prev_quarter_year, lit("-01-01"))))
        .when(prev_quarter == 2, to_date(concat(prev_quarter_year, lit("-04-01"))))
        .when(prev_quarter == 3, to_date(concat(prev_quarter_year, lit("-07-01"))))
        .when(prev_quarter == 4, to_date(concat(prev_quarter_year, lit("-10-01"))))
    )
    
    # Get the most recent purchase date for each customer
    customer_purchases = invoice_line_df.groupBy("customer_id").agg(
        max("invoice_date").alias("last_purchase_date"),
        count("*").alias("total_purchases"),
        sql_sum("line_total_amount").alias("total_spend"),
        expr("count(distinct invoice_id)").alias("total_invoices")
    )
    
    # Determine if the customer has purchased in the last quarter
    customer_purchases = customer_purchases.withColumn(
        "purchased_last_quarter",
        when(col("last_purchase_date") >= last_quarter_start, True).otherwise(False)
    )
    
    # Add quarter information for analysis
    customer_purchases = customer_purchases.withColumn(
        "last_purchase_quarter", 
        quarter(col("last_purchase_date"))
    )
    customer_purchases = customer_purchases.withColumn(
        "last_purchase_year", 
        year(col("last_purchase_date"))
    )
    
    # Add quarter start and end dates for the previous quarter
    customer_purchases = customer_purchases.withColumn(
        "quarter_start_date", 
        last_quarter_start
    )
    
    # Calculate the end date of the previous quarter
    quarter_end_date = (
        when(prev_quarter == 1, to_date(concat(prev_quarter_year, lit("-03-31"))))
        .when(prev_quarter == 2, to_date(concat(prev_quarter_year, lit("-06-30"))))
        .when(prev_quarter == 3, to_date(concat(prev_quarter_year, lit("-09-30"))))
        .when(prev_quarter == 4, to_date(concat(prev_quarter_year, lit("-12-31"))))
    )
    
    customer_purchases = customer_purchases.withColumn(
        "quarter_end_date", 
        quarter_end_date
    )
    
    # Calculate days since last purchase
    customer_purchases = customer_purchases.withColumn(
        "days_since_last_purchase",
        datediff(current_date, col("last_purchase_date"))
    )
    
    # Join with customer data to get customer details
    result_df = customer_df.join(
        customer_purchases,
        customer_df.customer_id == customer_purchases.customer_id,
        "left"
    ).drop(customer_purchases.customer_id)
    
    # Handle customers with no purchase history
    result_df = result_df.withColumn(
        "purchased_last_quarter",
        when(col("last_purchase_date").isNull(), False).otherwise(col("purchased_last_quarter"))
    )
    
    # Add customer status based on purchase activity
    result_df = result_df.withColumn(
        "customer_status",
        when(col("last_purchase_date").isNull(), "Never Purchased")
        .when(col("purchased_last_quarter"), "Active")
        .when(col("days_since_last_purchase") <= 180, "Recent")
        .when(col("days_since_last_purchase") <= 365, "Lapsed")
        .otherwise("Inactive")
    )
    
    # Add metadata
    result_df = result_df.withColumn("dw_created_at", current_timestamp())
    
    return log_dataframe_info(result_df, "customer_purchase_activity", customer_activity_logger)

# COMMAND ----------

# Function to create materialized view
@log_execution(customer_activity_logger)
def create_materialized_view(df, view_name):
    customer_activity_logger.info(f"Creating materialized view: {view_name}")
    
    if df is None:
        customer_activity_logger.error("No data available to create materialized view")
        return False
    
    target_path = f"{gold_path}/{view_name}"
    
    try:
        # Select only the required columns for the materialized view
        required_columns = [
            "customer_id",
            "customer_type",
            "customer_email_address",
            "registration_date",
            "last_purchase_date",
            "quarter_start_date",
            "quarter_end_date",
            "purchased_last_quarter",
            "days_since_last_purchase",
            "dw_created_at"  # Keep metadata column
        ]
        
        # Filter the DataFrame to only include the required columns
        filtered_df = df.select(required_columns)
        
        # Write data to Delta table
        filtered_df.write.format(gold_format).mode("overwrite").option("path", target_path).saveAsTable(f"{catalog}.{gold_schema}.{view_name}")
        
        # Set table properties for optimization
        spark.sql(f"""
        ALTER TABLE {catalog}.{gold_schema}.{view_name} SET TBLPROPERTIES (
            {materialized_view_properties}
        )
        """)
        
        customer_activity_logger.info(f"Successfully created aggregated materialized view: {view_name}")
        return True
    except Exception as e:
        customer_activity_logger.error(f"Error creating materialized view: {str(e)}")
        return False

# COMMAND ----------

# Main execution function
@log_execution(customer_activity_logger)
def process_customer_purchase_activity():
    customer_activity_logger.info("Processing customer purchase activity")
    
    # Read data from silver layer
    customer_df, invoice_line_df = read_silver_data()
    
    # Create customer purchase activity view
    activity_df = create_customer_purchase_activity(customer_df, invoice_line_df)
    
    # Create materialized view
    if activity_df is not None:
        create_materialized_view(activity_df, "agg_customer_purchase_activity")
        return activity_df
    else:
        customer_activity_logger.error("Failed to create customer purchase activity view")
        return None

# COMMAND ----------

# Execute the process
activity_df = process_customer_purchase_activity()
if activity_df is not None:
    # Select only the requested columns for display
    display_columns = [
        "customer_id",
        "customer_type",
        "customer_email_address",
        "registration_date",
        "last_purchase_date",
        "quarter_start_date",
        "quarter_end_date",
        "purchased_last_quarter",
        "days_since_last_purchase"
    ]
    
    # Create a filtered view with only the requested columns
    filtered_df = activity_df.select(display_columns)
    
    # Display the filtered DataFrame
    display(filtered_df)