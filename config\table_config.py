# Databricks notebook source
# MAGIC %md
# MAGIC # Table Configuration
# MAGIC
# MAGIC This notebook contains the configuration for all tables in the data pipeline, including:
# MAGIC - Primary keys
# MAGIC - Column mappings between layers
# MAGIC - Data quality checks
# MAGIC - Surrogate keys
# MAGIC
# MAGIC The configuration is organized by table and then by layer (silver, gold).

# COMMAND ----------

# Table configuration dictionary
table_config = {
  "tables": {
    "customer": {
      "silver": {
        "primary_key": "customer_id",
        "column_mapping_bronze_to_silver": {
          "cust_identifier": "customer_id",
          "name": "customer_full_name",
          "category": "customer_type",
          "mail": "customer_email_address",
          "telephone": "phone_number",
          "location": "address",
          "enroll_date": "registration_date",
          "status": "is_active",
          "membership_num": "loyalty_card_id",
          "loyalty_level": "loyalty_tier"
        },
        "data_quality_checks": {
          "not_null_cols": ["customer_id", "customer_full_name", "customer_type"],
          "unique_cols": ["customer_id"]
        }
      },
      "gold": {
        "primary_key": "pk_customer_id"
      }
    },
    "invoice": {
      "silver": {
        "primary_key": "invoice_id",
        "column_mapping_bronze_to_silver": {
          "invoice_code": "invoice_id",
          "invoice_type": "invoice_type",
          "status": "status",
          "ref_num": "reference_number",
          "emp_id": "employee_id",
          "method": "payment_method",
          "channel": "channel_type"
        },
        "data_quality_checks": {
          "not_null_cols": ["invoice_id", "customer_id", "site_id"],
          "unique_cols": ["invoice_id"]
        }
      },
      "gold": {
        "primary_key": "pk_invoice_id"
      }
    },
    "invoice_line": {
      "silver": {
        "primary_key": "invoice_line_id",
        "column_mapping_bronze_to_silver": {
          "line_id": "invoice_line_id",
          "invoice_id": "invoice_id",
          "discount_code": "discount_id",
          "item_code": "item_id",
          "tax_code": "tax_id",
          "cust_identifier": "customer_id",
          "invoice_date": "invoice_date",
          "qty": "quantity",
          "price": "unit_price",
          "discount_value": "discount_amount",
          "tax_value": "tax_amount",
          "total_value": "line_total_amount"
        },
        "data_quality_checks": {
          "not_null_cols": ["invoice_line_id",  "invoice_id", "invoice_date","item_id", "quantity"],
          "unique_cols": ["invoice_line_id"]
        }
      },
      "gold": {
        "primary_key": "pk_invoice_line_id"
      }
    }
  }
}