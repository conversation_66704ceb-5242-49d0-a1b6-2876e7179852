# Databricks notebook source
# MAGIC %md
# MAGIC # Table Configuration
# MAGIC
# MAGIC This notebook contains the configuration for all tables in the data pipeline, including:
# MAGIC - Primary keys
# MAGIC - Column mappings between layers
# MAGIC - Data quality checks
# MAGIC - Surrogate keys
# MAGIC
# MAGIC The configuration is organized by table and then by layer (silver, gold).

# COMMAND ----------

# Table configuration dictionary
table_config = {
  "tables": {
    "customer": {
      "silver": {
        "primary_key": "customer_id",
        "column_mapping_bronze_to_silver": {
          "cust_identifier": "customer_id",
          "name": "customer_full_name",
          "category": "customer_type",
          "mail": "customer_email_address",
          "telephone": "phone_number",
          "location": "address",
          "enroll_date": "registration_date",
          "status": "is_active",
          "membership_num": "loyalty_card_id",
          "loyalty_level": "loyalty_tier"
        },
        "data_quality_checks": {
          "not_null_cols": ["customer_id", "customer_full_name", "customer_type"],
          "unique_cols": ["customer_id"]
        }
      },
      "gold": {
        "primary_key": "pk_customer_id"
      }
    },
    "invoice": {
      "silver": {
        "primary_key": "invoice_id",
        "column_mapping_bronze_to_silver": {
          "invoice_code": "invoice_id",
          "invoice_type": "invoice_type",
          "status": "status",
          "ref_num": "reference_number",
          "emp_id": "employee_id",
          "method": "payment_method",
          "channel": "channel_type"
        },
        "data_quality_checks": {
          "not_null_cols": ["invoice_id", "customer_id", "site_id"],
          "unique_cols": ["invoice_id"]
        }
      },
      "gold": {
        "primary_key": "pk_invoice_id"
      }
    },
    "invoice_line": {
      "silver": {
        "primary_key": "invoice_line_id",
        "column_mapping_bronze_to_silver": {
          "line_id": "invoice_line_id",
          "invoice_id": "invoice_id",
          "discount_code": "discount_id",
          "item_code": "item_id",
          "tax_code": "tax_id",
          "cust_identifier": "customer_id",
          "site_code": "site_id",
          "invoice_date": "invoice_date",
          "qty": "quantity",
          "price": "unit_price",
          "discount_value": "discount_amount",
          "tax_value": "tax_amount",
          "total_value": "line_total_amount"
        },
        "data_quality_checks": {
          "not_null_cols": ["invoice_line_id",  "invoice_id", "invoice_date","item_id", "quantity"],
          "unique_cols": ["invoice_line_id"]
        }
      },
      "gold": {
        "primary_key": "pk_invoice_line_id"
      }
    },
    "item": {
      "silver": {
        "primary_key": "item_id",
        "column_mapping_bronze_to_silver": {
          "item_code": "item_id",
          "product_code": "product_id",
          "name": "item_name",
          "uom": "unit_of_measure",
          "pack_size": "package_size",
          "measure": "measurement",
          "info": "nutritional_info",
          "mfd": "manufacture_date",
          "exp": "expiry_date",
          "status": "is_active"
        },
        "data_quality_checks": {
          "not_null_cols": ["item_id", "product_id", "item_name"],
          "unique_cols": ["item_id"]
        }
      },
      "gold": {
        "primary_key": "pk_item_id"
      }
    },
    "product": {
      "silver": {
        "primary_key": "product_id",
        "column_mapping_bronze_to_silver": {
          "product_code": "product_id",
          "name": "product_name",
          "category": "product_category",
          "sub_category": "product_sub_category",
          "desc": "product_description",
          "brand": "brand_name",
          "allergen_info": "allergen_information",
          "shelf_life": "shelf_life_days",
          "private_label": "is_private_label",
          "organic": "is_organic",
          "perishable": "is_perishable",
          "status": "is_active"
        },
        "data_quality_checks": {
          "not_null_cols": ["product_id", "product_name", "product_category"],
          "unique_cols": ["product_id"]
        }
      },
      "gold": {
        "primary_key": "pk_product_id"
      }
    },
    "cost": {
      "silver": {
        "primary_key": "cost_id",
        "column_mapping_bronze_to_silver": {
          "cost_code": "cost_id",
          "item_code": "item_id",
          "cost_type": "cost_type",
          "sell_value": "selling_price",
          "buy_value": "purchase_cost",
          "margin_pct": "margin_percentage",
          "status": "is_active"
        },
        "data_quality_checks": {
          "not_null_cols": ["cost_id", "item_id", "selling_price", "purchase_cost"],
          "unique_cols": ["cost_id"]
        }
      },
      "gold": {
        "primary_key": "pk_cost_id"
      }
    },
    "product_performance": {
      "gold": {
        "primary_key": "product_id",
        "table_name": "product_performance",
        "description": "Product performance metrics aggregated from sales data",
        "source_tables": ["invoice_line", "item", "product", "cost"],
        "columns": {
          "product_id": "string",
          "product_name": "string",
          "product_category": "string",
          "product_sub_category": "string",
          "brand_name": "string",
          "is_perishable": "boolean",
          "total_quantity_sold": "bigint",
          "total_sales_amount": "decimal(15,2)",
          "total_transactions": "bigint",
          "avg_selling_price": "decimal(10,2)",
          "top_selling_item_id": "string",
          "top_selling_item_name": "string",
          "top_item_quantity": "bigint",
          "top_item_sales": "decimal(15,2)",
          "avg_margin_per_item": "decimal(10,2)",
          "avg_item_price": "decimal(10,2)",
          "avg_item_cost": "decimal(10,2)",
          "total_margin_amount": "decimal(15,2)",
          "data_quality_score": "decimal(5,2)",
          "last_updated": "timestamp"
        }
      }
    }
  }
}