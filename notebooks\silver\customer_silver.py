# Databricks notebook source
# MAGIC %md
# MAGIC # Customer - Bronze to Silver Transformation
# MAGIC
# MAGIC This notebook processes customer data from the bronze layer to the silver layer. It applies data type conversions, handles timestamp formatting, standardizes values, and performs data quality checks.

# COMMAND ----------

# MAGIC %run ../../start_up

# COMMAND ----------

# MAGIC %run ../../utils/silver_utils

# COMMAND ----------

# MAGIC %run ../../utils/schema_utils

# COMMAND ----------

from pyspark.sql.functions import (
    input_file_name, current_timestamp, lit, col, to_timestamp, when, 
    upper, regexp_extract, datediff, expr
)
from pyspark.sql.utils import AnalysisException
from delta.tables import DeltaTable

# Use the logger configuration from startup with log_path from config
customer_silver_logger = create_logger(component_log_levels=component_log_levels)
customer_silver_logger.info("Initializing notebook")
 
# Extract frequently used config values into variables
catalog = pipeline_config["catalog"]
target_date = pipeline_config["default_processing_date"]
bronze_schema = pipeline_config["schemas"]["bronze"]
silver_schema = pipeline_config["schemas"]["silver"]
bronze_path = pipeline_config["paths"]["bronze_path"]
silver_path = pipeline_config["paths"]["silver_path"]
silver_format = pipeline_config["file_formats"]["silver"]
delta_properties = pipeline_config["delta_properties"]

# Switch to catalog
spark.sql(f"USE CATALOG {catalog}")

# COMMAND ----------

# Get customer table configuration from table_config
customer_config = table_config["tables"]["customer"]
customer_silver_logger.info(f"Loaded customer configuration from table_config")

# Table metadata for customer table
table_metadata = {
    "primary_key": customer_config["silver"]["primary_key"],
    "column_mapping": customer_config["silver"]["column_mapping_bronze_to_silver"],
    "timestamp_columns": ["registration_date", "created_timestamp", "modified_timestamp"],
    "numeric_columns": ["primary_key"],
    "boolean_columns": ["is_active"],
    "customer_types": ["Business", "Individual", "Corporate", "Government", "Non-profit"],
    "loyalty_tiers": ["Bronze", "Silver", "Gold", "Platinum"]
}

# Log the column mapping for debugging
customer_silver_logger.info(f"Column mapping: {table_metadata['column_mapping']}")

# COMMAND ----------

# Function to apply transformations based on table metadata
@log_execution(customer_silver_logger)
def apply_transformations(df):
    customer_silver_logger.info("Applying transformations to customer data")
    
    # Apply column mapping from bronze to silver
    column_mapping = table_metadata["column_mapping"]
    df = apply_column_mapping(df, column_mapping)
    
    # Get the schema for customer table in silver layer
    customer_schema = get_schema("customer", "silver")
    
    # Apply schema to enforce data types
    if customer_schema:
        customer_silver_logger.info("Applying explicit schema for data type enforcement")
        df = apply_schema(df, customer_schema)
    else:
        # Fall back to individual conversions if schema is not available
        customer_silver_logger.info("Falling back to individual data type conversions")
        # Apply timestamp conversions
        df = convert_timestamp_columns(df, table_metadata["timestamp_columns"], "M/d/yyyy")
        
        # Apply numeric conversions
        df = convert_numeric_columns(df, table_metadata["numeric_columns"])
        
        # Apply boolean conversions
        df = convert_boolean_columns(df, table_metadata["boolean_columns"])
    
    # Validate email format
    if "customer_email_address" in df.columns:
        df = validate_regex_pattern(
            df, 
            "customer_email_address", 
            "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,6}$",
            "email_valid"
        )
    
    # Validate phone number format and standardize
    if "phone_number" in df.columns:
        df = standardize_phone_numbers(df, ["phone_number"])
        df = validate_regex_pattern(
            df, 
            "phone_number", 
            "^[0-9]{3}-[0-9]{3}-[0-9]{4}$",
            "phone_valid"
        )
    
    # Standardize and validate customer type
    if "customer_type" in df.columns:
        df = standardize_categorical_values(
            df, 
            "customer_type", 
            table_metadata["customer_types"]
        )
    
    # Standardize and validate loyalty tier
    if "loyalty_tier" in df.columns:
        df = standardize_categorical_values(
            df, 
            "loyalty_tier", 
            table_metadata["loyalty_tiers"]
        )
    
    # Extract address components
    if "address" in df.columns:
        # Extract postal code (assuming Canadian format: A1A 1A1)
        df = df.withColumn(
            "postal_code", 
            regexp_extract(col("address"), "[A-Z][0-9][A-Z]\\s?[0-9][A-Z][0-9]", 0)
        )
        
        # Extract province (assuming Canadian province codes)
        df = df.withColumn(
            "province", 
            regexp_extract(col("address"), "\\b(AB|BC|MB|NB|NL|NS|NT|NU|ON|PE|QC|SK|YT)\\b", 0)
        )
        
        # Extract city (assuming format: "123 Main St, City, Province A1A 1A1")
        df = df.withColumn(
            "city", 
            regexp_extract(col("address"), ", ([^,]+),", 1)
        )
    
    # Calculate customer tenure in days
    if "registration_date" in df.columns:
        df = df.withColumn(
            "customer_tenure_days",
            datediff(current_timestamp(), col("registration_date"))
        )
    
    # Validate primary key
    df, pk_valid = validate_primary_key(df, table_metadata["primary_key"])
    if not pk_valid:
        customer_silver_logger.warning(f"Primary key validation failed for {table_metadata['primary_key']}")
    
    # Calculate data quality score
    df = calculate_data_quality_score(df)
    
    # Add processing timestamp
    df = df.withColumn("processed_at", current_timestamp())
    
    return log_dataframe_info(df, "customer_transformed", customer_silver_logger)

# COMMAND ----------

# Main processing function
@log_execution(customer_silver_logger)
def process_bronze_to_silver(target_date=None):
    customer_silver_logger.info(f"Processing customer data from bronze to silver for date: {target_date if target_date else 'all'}")
    
    # Get the schema for customer table in silver layer
    customer_schema = get_schema("customer", "silver")
    customer_silver_logger.info(f"Using explicit schema for customer table: {customer_schema}")
    
    # Read bronze data directly using the utility function
    bronze_df = read_bronze_data(
        spark=spark,
        bronze_path=bronze_path,
        table_name="customer",
        date=target_date,
        logger=customer_silver_logger
    )
    
    if bronze_df is None or bronze_df.isEmpty():
        customer_silver_logger.warning(f"No bronze data found for customer on date {target_date}")
        return None
    
    # Apply transformations
    silver_df = apply_transformations(bronze_df)
    
    # Write to silver layer directly using the utility function
    final_df = write_silver_data(
        spark=spark,
        df=silver_df,
        silver_path=silver_path,
        table_name="customer_cleaned",
        primary_key=table_metadata["primary_key"],
        column_mapping=None,  # Don't apply column mapping again as it's already applied in apply_transformations
        catalog=catalog,
        schema=silver_schema,
        format=silver_format,
        logger=customer_silver_logger
    )
    
    customer_silver_logger.info("Completed bronze to silver processing for customer_cleaned")
    return final_df

# COMMAND ----------

# Process customer data
silver_df = process_bronze_to_silver(target_date)
if silver_df is not None:
    log_dataframe_info(silver_df, "customer_silver_final", customer_silver_logger)
    display(silver_df)