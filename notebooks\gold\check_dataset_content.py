# Databricks notebook source
# MAGIC %md
# MAGIC # Check Dataset Content for Pricing Gold Table
# MAGIC
# MAGIC This notebook examines the actual data content to determine what columns can be derived

# COMMAND ----------

# MAGIC %run ../../start_up

# COMMAND ----------

from pyspark.sql.functions import col, count, sum as spark_sum, avg, min as spark_min, max as spark_max, isnan, isnull

# Use the logger configuration from startup
check_logger = create_logger(component_log_levels=component_log_levels)
check_logger.info("🔍 Starting dataset content analysis")

# Extract config values
catalog = pipeline_config["catalog"]
silver_schema = pipeline_config["schemas"]["silver"]

# COMMAND ----------

# MAGIC %md
# MAGIC ## Check Silver Tables Structure and Sample Data

# COMMAND ----------

print("📊 ANALYZING SILVER TABLES FOR PRICING GOLD TABLE")
print("=" * 60)

tables_to_check = ["cost", "item", "product", "invoice_line_cleaned"]

for table_name in tables_to_check:
    print(f"\n🔍 TABLE: {table_name}")
    print("-" * 40)
    
    try:
        df = spark.table(f"{catalog}.{silver_schema}.{table_name}")
        
        print(f"📊 Total rows: {df.count()}")
        print(f"📋 Total columns: {len(df.columns)}")
        print(f"📝 Columns: {df.columns}")
        
        print(f"\n📄 Sample data (first 3 rows):")
        df.show(3, truncate=False)
        
        print(f"\n📈 Data types:")
        df.printSchema()
        
    except Exception as e:
        print(f"❌ Error reading {table_name}: {str(e)}")

# COMMAND ----------

# MAGIC %md
# MAGIC ## Detailed Analysis: COST Table

# COMMAND ----------

print("💰 DETAILED COST TABLE ANALYSIS")
print("=" * 50)

try:
    cost_df = spark.table(f"{catalog}.{silver_schema}.cost")
    
    print("📊 Cost table statistics:")
    print(f"Total rows: {cost_df.count()}")
    
    # Check for pricing-related columns
    pricing_columns = ["selling_price", "purchase_cost", "margin_amount", "cost_type"]
    available_pricing_cols = [col for col in pricing_columns if col in cost_df.columns]
    
    print(f"💰 Available pricing columns: {available_pricing_cols}")
    
    if available_pricing_cols:
        print("\n📈 Pricing data statistics:")
        for col_name in available_pricing_cols:
            if col_name in cost_df.columns:
                stats = cost_df.select(
                    count(col_name).alias("count"),
                    count("*").alias("total"),
                    spark_min(col_name).alias("min_val"),
                    spark_max(col_name).alias("max_val"),
                    avg(col_name).alias("avg_val")
                ).collect()[0]
                
                print(f"  {col_name}:")
                print(f"    Non-null: {stats['count']}/{stats['total']} ({stats['count']/stats['total']*100:.1f}%)")
                if stats['min_val'] is not None:
                    print(f"    Range: {stats['min_val']:.2f} - {stats['max_val']:.2f}")
                    print(f"    Average: {stats['avg_val']:.2f}")
    
    # Check cost types if available
    if "cost_type" in cost_df.columns:
        print(f"\n🏷️  Cost types:")
        cost_df.groupBy("cost_type").count().show()
    
    # Sample cost data
    print(f"\n📄 Sample cost data:")
    cost_df.select("item_id", "selling_price", "purchase_cost", "margin_amount").show(5)
    
except Exception as e:
    print(f"❌ Error analyzing cost table: {str(e)}")

# COMMAND ----------

# MAGIC %md
# MAGIC ## Detailed Analysis: INVOICE_LINE Table

# COMMAND ----------

print("🧾 DETAILED INVOICE_LINE TABLE ANALYSIS")
print("=" * 50)

try:
    invoice_df = spark.table(f"{catalog}.{silver_schema}.invoice_line_cleaned")
    
    print("📊 Invoice line statistics:")
    print(f"Total rows: {invoice_df.count()}")
    
    # Check for sales pricing columns
    sales_columns = ["quantity", "unit_price", "line_total_amount", "sales_amount", "net_amount"]
    available_sales_cols = [col for col in sales_columns if col in invoice_df.columns]
    
    print(f"💵 Available sales columns: {available_sales_cols}")
    
    if available_sales_cols:
        print("\n📈 Sales data statistics:")
        for col_name in available_sales_cols:
            if col_name in invoice_df.columns:
                stats = invoice_df.select(
                    count(col_name).alias("count"),
                    count("*").alias("total"),
                    spark_min(col_name).alias("min_val"),
                    spark_max(col_name).alias("max_val"),
                    avg(col_name).alias("avg_val")
                ).collect()[0]
                
                print(f"  {col_name}:")
                print(f"    Non-null: {stats['count']}/{stats['total']} ({stats['count']/stats['total']*100:.1f}%)")
                if stats['min_val'] is not None:
                    print(f"    Range: {stats['min_val']:.2f} - {stats['max_val']:.2f}")
                    print(f"    Average: {stats['avg_val']:.2f}")
    
    # Calculate actual unit price if possible
    if "line_total_amount" in invoice_df.columns and "quantity" in invoice_df.columns:
        print(f"\n🧮 Calculated actual unit prices:")
        actual_prices = invoice_df.filter(col("quantity") > 0).withColumn(
            "calculated_unit_price", 
            col("line_total_amount") / col("quantity")
        )
        actual_prices.select("item_id", "quantity", "line_total_amount", "calculated_unit_price").show(5)
    
except Exception as e:
    print(f"❌ Error analyzing invoice_line table: {str(e)}")

# COMMAND ----------

# MAGIC %md
# MAGIC ## Join Analysis: Cost + Sales Data

# COMMAND ----------

print("🔗 JOIN ANALYSIS: COST + SALES DATA")
print("=" * 50)

try:
    cost_df = spark.table(f"{catalog}.{silver_schema}.cost")
    invoice_df = spark.table(f"{catalog}.{silver_schema}.invoice_line_cleaned")
    item_df = spark.table(f"{catalog}.{silver_schema}.item")
    
    # Join cost and sales data via item_id
    if "item_id" in cost_df.columns and "item_id" in invoice_df.columns:
        
        # Create pricing comparison
        pricing_analysis = invoice_df.filter(col("quantity") > 0).withColumn(
            "actual_unit_price", 
            col("line_total_amount") / col("quantity")
        ).join(
            cost_df.select("item_id", "selling_price", "purchase_cost", "margin_amount"),
            "item_id",
            "inner"
        ).join(
            item_df.select("item_id", "product_id"),
            "item_id", 
            "inner"
        )
        
        join_count = pricing_analysis.count()
        print(f"✅ Successful joins: {join_count} records")
        
        if join_count > 0:
            print(f"\n📊 Pricing comparison sample:")
            pricing_analysis.select(
                "item_id", 
                "product_id",
                "selling_price", 
                "actual_unit_price",
                "purchase_cost",
                "margin_amount"
            ).show(10)
            
            # Calculate price variance
            price_variance = pricing_analysis.withColumn(
                "price_variance",
                col("actual_unit_price") - col("selling_price")
            ).withColumn(
                "price_variance_pct",
                (col("actual_unit_price") - col("selling_price")) / col("selling_price") * 100
            )
            
            print(f"\n📈 Price variance analysis:")
            price_variance.select(
                avg("price_variance").alias("avg_price_variance"),
                spark_min("price_variance").alias("min_variance"),
                spark_max("price_variance").alias("max_variance"),
                avg("price_variance_pct").alias("avg_variance_pct")
            ).show()
        else:
            print("⚠️  No matching records found between cost and sales data")
    else:
        print("❌ Cannot join - missing item_id columns")
        
except Exception as e:
    print(f"❌ Error in join analysis: {str(e)}")

# COMMAND ----------

# MAGIC %md
# MAGIC ## Feasibility Assessment

# COMMAND ----------

print("✅ FEASIBILITY ASSESSMENT FOR PRICING GOLD TABLE")
print("=" * 60)

print("""
Based on the data analysis above, here's what we can realistically derive:

DEFINITELY POSSIBLE:
✅ product_id, product_name, category, sub_category, brand (from product + item tables)
✅ current_selling_price (from cost.selling_price)
✅ current_purchase_cost (from cost.purchase_cost)  
✅ current_margin_amount (from cost.margin_amount)
✅ current_margin_percentage (calculated: margin_amount/selling_price*100)
✅ actual_avg_sales_price (calculated: line_total_amount/quantity from invoice_line)
✅ price_variance (calculated: actual_price - selling_price)
✅ last_updated (current timestamp)

POSSIBLY AVAILABLE (depends on data quality):
⚠️  total_margin_contribution (if we have enough sales volume data)
⚠️  pricing_efficiency_score (can be calculated if we have good price/sales correlation)

CHALLENGING/REQUIRES BUSINESS RULES:
❓ optimization_recommendation (needs business logic rules)

RECOMMENDATION:
Start with 12-13 columns that we can definitely derive from the data.
Add business logic columns later based on business requirements.
""")

check_logger.info("🏁 Dataset content analysis complete")
