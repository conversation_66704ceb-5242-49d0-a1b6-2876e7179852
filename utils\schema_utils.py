# Databricks notebook source
# MAGIC %md
# MAGIC # Schema Utility Functions
# MAGIC
# MAGIC This notebook contains utility functions for schema management, including:
# MAGIC - Schema definition
# MAGIC - Schema validation
# MAGIC - Schema application
# MAGIC
# MAGIC These functions are designed to be reused across all layers of the medallion architecture.

# COMMAND ----------

# MAGIC %run ../start_up

# COMMAND ----------

# Run the startup notebook to get access to common utilities and configurations
from pyspark.sql.types import (
    StructType, StructField, StringType, IntegerType, LongType,
    FloatType, DoubleType, BooleanType, TimestampType, DateType,
    DecimalType, ArrayType, MapType
)
from pyspark.sql.functions import col, lit, when
import json
import re

# COMMAND ----------

# Initialize logger using log_path from config
# component_log_levels is imported from start_up notebook
schema_utils_logger = create_logger(component_log_levels=component_log_levels)
schema_utils_logger.info("Initializing schema utilities")

# COMMAND ----------

# Customer table schema definitions
table_schemas = {
    "customer": {
        "bronze": {
            "cust_identifier": StringType(),
            "name": StringType(),
            "category": StringType(),
            "mail": StringType(),
            "telephone": StringType(),
            "location": StringType(),
            "enroll_date": StringType(),
            "status": StringType(),
            "level": StringType(),
            "membership_num": StringType(),
            "source_file_path": StringType(),
            "ingestion_timestamp": TimestampType(),
            "ingestion_date": DateType()
        },
        "silver": {
            "customer_id": StringType(),
            "customer_full_name": StringType(),
            "customer_type": StringType(),
            "customer_email_address": StringType(),
            "phone_number": StringType(),
            "address": StringType(),
            "street_address": StringType(),
            "city": StringType(),
            "state_province": StringType(),
            "postal_code": StringType(),
            "registration_date": TimestampType(),
            "is_active": BooleanType(),
            "loyalty_tier": StringType(),
            "loyalty_card_id": StringType(),
            "quality_score_pct": DoubleType(),
            "source_file_path": StringType(),
            "ingestion_timestamp": TimestampType(),
            "ingestion_date": DateType()
        },
        "gold": {
            "sk_customer_id": StringType(),
            "pk_customer_id": StringType(),
            "customer_full_name": StringType(),
            "customer_type": StringType(),
            "customer_email_address": StringType(),
            "phone_number": StringType(),
            "address": StringType(),
            "street_address": StringType(),
            "city": StringType(),
            "state_province": StringType(),
            "postal_code": StringType(),
            "registration_date": TimestampType(),
            "is_active": BooleanType(),
            "loyalty_card_id": StringType(),
            "loyalty_tier": StringType(),
            "total_lifetime_value": DoubleType(),
            "total_order_count": IntegerType(),
            "avg_order_value": DoubleType(),
            "first_purchase_date": TimestampType(),
            "last_purchase_date": TimestampType(),
            "days_since_purchase": IntegerType(),
            "customer_tenure_days": IntegerType(),
            "avg_monthly_frequency": DoubleType(),
            "customer_segment": StringType(),
            "quality_score_pct": DoubleType(),
            "created_at": TimestampType(),
            "updated_at": TimestampType(),
            "created_by": StringType(),
            "modified_by": StringType()
        }
    },
    "item": {
        "bronze": {
            "item_code": StringType(),
            "product_code": StringType(),
            "name": StringType(),
            "uom": StringType(),
            "pack_size": StringType(),
            "measure": StringType(),
            "info": StringType(),
            "mfd": StringType(),
            "exp": StringType(),
            "status": StringType(),
            "source_file_path": StringType(),
            "ingestion_timestamp": TimestampType(),
            "ingestion_date": DateType()
        },
        "silver": {
            "item_id": StringType(),
            "product_id": StringType(),
            "item_name": StringType(),
            "unit_of_measure": StringType(),
            "package_size": DoubleType(),
            "measurement": StringType(),
            "measurement_value": DoubleType(),
            "measurement_unit": StringType(),
            "nutritional_info": StringType(),
            "manufacture_date": TimestampType(),
            "expiry_date": TimestampType(),
            "shelf_life_days": IntegerType(),
            "is_active": BooleanType(),
            "data_quality_score": DoubleType(),
            "processed_at": TimestampType()
        },
        "gold": {
            "pk_item_id": StringType(),
            "product_id": StringType(),
            "item_name": StringType(),
            "unit_of_measure": StringType(),
            "package_size": DoubleType(),
            "nutritional_info": StringType(),
            "shelf_life_days": IntegerType(),
            "is_active": BooleanType(),
            "created_at": TimestampType(),
            "updated_at": TimestampType()
        }
    },
    "product": {
        "bronze": {
            "product_code": StringType(),
            "name": StringType(),
            "category": StringType(),
            "sub_category": StringType(),
            "desc": StringType(),
            "brand": StringType(),
            "allergen_info": StringType(),
            "shelf_life": StringType(),
            "private_label": StringType(),
            "organic": StringType(),
            "perishable": StringType(),
            "status": StringType(),
            "source_file_path": StringType(),
            "ingestion_timestamp": TimestampType(),
            "ingestion_date": DateType()
        },
        "silver": {
            "product_id": StringType(),
            "product_name": StringType(),
            "product_category": StringType(),
            "product_sub_category": StringType(),
            "product_description": StringType(),
            "brand_name": StringType(),
            "allergen_information": StringType(),
            "shelf_life_days": IntegerType(),
            "is_private_label": BooleanType(),
            "is_organic": BooleanType(),
            "is_perishable": BooleanType(),
            "is_active": BooleanType(),
            "data_quality_score": DoubleType(),
            "processed_at": TimestampType()
        },
        "gold": {
            "pk_product_id": StringType(),
            "product_name": StringType(),
            "product_category": StringType(),
            "product_sub_category": StringType(),
            "product_description": StringType(),
            "brand_name": StringType(),
            "allergen_information": StringType(),
            "shelf_life_days": IntegerType(),
            "is_private_label": BooleanType(),
            "is_organic": BooleanType(),
            "is_perishable": BooleanType(),
            "is_active": BooleanType(),
            "created_at": TimestampType(),
            "updated_at": TimestampType()
        }
    },
    "cost": {
        "bronze": {
            "cost_code": StringType(),
            "item_code": StringType(),
            "cost_type": StringType(),
            "sell_value": StringType(),
            "buy_value": StringType(),
            "margin_pct": StringType(),
            "status": StringType(),
            "source_file_path": StringType(),
            "ingestion_timestamp": TimestampType(),
            "ingestion_date": DateType()
        },
        "silver": {
            "cost_id": StringType(),
            "item_id": StringType(),
            "cost_type": StringType(),
            "selling_price": DoubleType(),
            "purchase_cost": DoubleType(),
            "margin_percentage": DoubleType(),
            "margin_amount": DoubleType(),
            "is_active": BooleanType(),
            "data_quality_score": DoubleType(),
            "processed_at": TimestampType()
        },
        "gold": {
            "pk_cost_id": StringType(),
            "item_id": StringType(),
            "cost_type": StringType(),
            "selling_price": DoubleType(),
            "purchase_cost": DoubleType(),
            "margin_percentage": DoubleType(),
            "margin_amount": DoubleType(),
            "is_active": BooleanType(),
            "created_at": TimestampType(),
            "updated_at": TimestampType()
        }
    },
    "invoice_line": {
        "bronze": {
            "line_id": StringType(),
            "invoice_id": StringType(),
            "discount_code": StringType(),
            "item_code": StringType(),
            "tax_code": StringType(),
            "cust_identifier": StringType(),
            "site_code": StringType(),
            "invoice_date": StringType(),
            "qty": StringType(),
            "price": StringType(),
            "discount_value": StringType(),
            "tax_value": StringType(),
            "total_value": StringType(),
            "source_file_path": StringType(),
            "ingestion_timestamp": TimestampType(),
            "ingestion_date": DateType()
        },
        "silver": {
            "invoice_line_id": StringType(),
            "invoice_id": StringType(),
            "discount_id": StringType(),
            "item_id": StringType(),
            "tax_id": StringType(),
            "customer_id": StringType(),
            "site_id": StringType(),
            "invoice_date": TimestampType(),
            "quantity": DoubleType(),
            "unit_price": DoubleType(),
            "discount_amount": DoubleType(),
            "tax_amount": DoubleType(),
            "line_total_amount": DoubleType(),
            "net_amount": DoubleType(),
            "calculated_total": DoubleType(),
            "data_quality_score": DoubleType(),
            "processed_at": TimestampType()
        },
        "gold": {
            "pk_invoice_line_id": StringType(),
            "invoice_id": StringType(),
            "item_id": StringType(),
            "customer_id": StringType(),
            "site_id": StringType(),
            "invoice_date": TimestampType(),
            "quantity": DoubleType(),
            "unit_price": DoubleType(),
            "discount_amount": DoubleType(),
            "tax_amount": DoubleType(),
            "line_total_amount": DoubleType(),
            "created_at": TimestampType(),
            "updated_at": TimestampType()
        }
    },
    "product_performance": {
        "gold": {
            # Essential business metrics only (13 columns)
            "product_id": StringType(),
            "product_name": StringType(),
            "category": StringType(),                    # Renamed from product_category
            "sub_category": StringType(),               # Renamed from product_sub_category
            "brand": StringType(),                      # Renamed from brand_name
            "total_quantity_sold": LongType(),
            "total_sales_amount": DoubleType(),
            "top_selling_item": StringType(),           # Renamed from top_selling_item_name
            "is_perishable": BooleanType(),
            "avg_margin_per_item": DoubleType(),
            "total_margin_amount": DoubleType(),
            "data_quality_score": DoubleType(),
            "last_updated": TimestampType()
        }
    }
}

# COMMAND ----------

@log_execution(schema_utils_logger)
def get_schema(table_name, layer):
    """Get the schema for a specific table and layer.

    Args:
        table_name: Name of the table
        layer: Layer name (bronze, silver, gold)

    Returns:
        StructType schema for the table
    """
    schema_utils_logger.info(f"Getting schema for {table_name} in {layer} layer")

    if table_name not in table_schemas or layer not in table_schemas[table_name]:
        schema_utils_logger.warning(f"No schema defined for {table_name} in {layer} layer")
        return None

    schema_dict = table_schemas[table_name][layer]
    schema_fields = [StructField(name, data_type, True) for name, data_type in schema_dict.items()]
    return StructType(schema_fields)

# COMMAND ----------

@log_execution(schema_utils_logger)
def apply_schema(df, schema):
    """Apply a schema to a DataFrame.

    Args:
        df: Source DataFrame
        schema: StructType schema to apply

    Returns:
        DataFrame with applied schema
    """
    schema_utils_logger.info("Applying schema to DataFrame")

    if schema is None:
        schema_utils_logger.warning("No schema provided, returning original DataFrame")
        return df

    # Apply schema by casting each column to the specified type
    for field in schema.fields:
        if field.name in df.columns:
            df = df.withColumn(field.name, col(field.name).cast(field.dataType))

    return df

# COMMAND ----------

@log_execution(schema_utils_logger)
def validate_schema(df, schema):
    """Validate that a DataFrame conforms to a schema.

    Args:
        df: Source DataFrame
        schema: StructType schema to validate against

    Returns:
        Tuple of (is_valid, validation_errors)
    """
    schema_utils_logger.info("Validating DataFrame against schema")

    if schema is None:
        schema_utils_logger.warning("No schema provided, validation skipped")
        return True, []

    validation_errors = []

    # Check for missing required columns
    schema_fields = {field.name for field in schema.fields}
    df_columns = set(df.columns)
    missing_columns = schema_fields - df_columns

    if missing_columns:
        error = f"Missing required columns: {missing_columns}"
        schema_utils_logger.warning(error)
        validation_errors.append(error)

    # Check data types
    for field in schema.fields:
        if field.name in df.columns:
            df_type = df.schema[field.name].dataType
            if str(df_type) != str(field.dataType):
                error = f"Column {field.name} has type {df_type}, expected {field.dataType}"
                schema_utils_logger.warning(error)
                validation_errors.append(error)

    is_valid = len(validation_errors) == 0
    return is_valid, validation_errors

# COMMAND ----------

@log_execution(schema_utils_logger)
def read_with_schema(spark, path, format, schema=None, options=None):
    """Read data with a specified schema.

    Args:
        spark: Spark session
        path: Path to the data
        format: Data format (csv, parquet, delta, etc.)
        schema: Optional schema to apply
        options: Optional dictionary of options

    Returns:
        DataFrame with the specified schema
    """
    schema_utils_logger.info(f"Reading data from {path} with format {format}")

    # Initialize reader
    reader = spark.read.format(format)

    # Apply options if provided
    if options:
        for key, value in options.items():
            reader = reader.option(key, value)

    # Apply schema if provided
    if schema:
        reader = reader.schema(schema)
        schema_utils_logger.info("Using explicit schema")
    else:
        schema_utils_logger.info("Using inferred schema")

    # Read the data
    df = reader.load(path)

    return df