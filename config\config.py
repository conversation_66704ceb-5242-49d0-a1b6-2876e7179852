# Databricks notebook source
# MAGIC %md
# MAGIC # Pipeline Configuration
# MAGIC
# MAGIC This notebook contains all the configuration parameters for the data pipeline.

# COMMAND ----------

# Storage paths
# base_path = "abfss://<EMAIL>/demp_pipeline"
base_path = "abfss://<EMAIL>/catalog_sobeys_uc"
landing_path = f"{base_path}/landing"
bronze_path = f"{base_path}/bronze"
silver_path = f"{base_path}/silver"
gold_path = f"{base_path}/gold"
log_path = f"{base_path}/logs"

# Catalog and schema names
catalog = "sobeys_uc"
bronze_schema = "bronze"
silver_schema = "silver"
gold_schema = "gold"

# Default processing date (can be overridden)
default_processing_date = "2025-05-18" #current_date()

# Delta table properties for schema evolution
delta_schema_evolution_properties = """
    'delta.columnMapping.mode' = 'name',
    'delta.schemaEvolution.enabled' = 'true',
    'delta.autoOptimize.optimizeWrite' = 'true',
    'delta.autoOptimize.autoCompact' = 'true'
"""

# Materialized view properties
materialized_view_properties = """
    'delta.autoOptimize.optimizeWrite' = 'true',
    'delta.autoOptimize.autoCompact' = 'true'
"""

# File formats for each layer
file_formats = {
    "landing": "csv",
    "bronze": "parquet",
    "silver": "delta",
    "gold": "delta"
}

# File options for each layer
file_options = {
    "landing": {
        "header": "true",
        "inferSchema": "false"
    },
    "bronze": {
        "mergeSchema": "true",
        "overwriteSchema": "true"
    },
    "silver": {
        "mergeSchema": "true"
    },
    "gold": {
        "mergeSchema": "true"
    }
}

# Tables to process in the pipeline
# This can be overridden in individual notebooks if needed
tables_to_process = [
    "customer",
    "invoice_line",
    "item",
    "product",
    "cost",
    # Uncomment tables below as needed
    # "invoice",
    # "site",
    # "discount",
    # "tax"
]

# Export configuration as a dictionary
pipeline_config = {
    "paths": {
        "base_path": base_path,
        "landing_path": landing_path,
        "bronze_path": bronze_path,
        "silver_path": silver_path,
        "gold_path": gold_path,
        "log_path": log_path
    },
    "catalog": catalog,
    "schemas": {
        "bronze": bronze_schema,
        "silver": silver_schema,
        "gold": gold_schema
    },
    "default_processing_date": default_processing_date,
    "delta_properties": delta_schema_evolution_properties,
    "materialized_view_properties": materialized_view_properties,
    "file_formats": file_formats,
    "file_options": file_options,
    "tables_to_process": tables_to_process
}