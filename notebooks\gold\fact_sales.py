# Databricks notebook source
# MAGIC %md
# MAGIC # Fact Sales - Silver to Gold Transformation
# MAGIC
# MAGIC This notebook creates a fact_sales table in the gold layer by joining and aggregating data from the invoice and invoice_line tables in the silver layer. It provides a comprehensive view of sales data for analytics and reporting.

# COMMAND ----------

# MAGIC %run ../../start_up

# COMMAND ----------

from pyspark.sql.functions import (
    current_timestamp, lit, col, to_date, date_format, concat, lpad,
    year, month, dayofmonth, quarter, sum as sql_sum, count, avg, min, max,
    when, coalesce, expr
)
from pyspark.sql.utils import AnalysisException
from delta.tables import DeltaTable

# Use the logger configuration from startup
fact_sales_logger = create_logger(log_file=log_file, component_log_levels=component_log_levels)
fact_sales_logger.info("Initializing notebook")
 
# Extract frequently used config values into variables
catalog = pipeline_config["catalog"]
target_date = pipeline_config["default_processing_date"]
silver_schema = pipeline_config["schemas"]["silver"]
gold_schema = pipeline_config["schemas"]["gold"]
silver_path = pipeline_config["paths"]["silver_path"]
gold_path = pipeline_config["paths"]["gold_path"]
gold_format = pipeline_config["file_formats"]["gold"]
delta_properties = pipeline_config["delta_properties"]

# Switch to catalog
spark.sql(f"USE CATALOG {catalog}")

# COMMAND ----------

# Function to read data from silver layer
@log_execution(fact_sales_logger)
def read_silver_data():
    fact_sales_logger.info("Reading silver data for invoice and invoice_line tables")
    
    # Read invoice data
    try:
        invoice_df = spark.table(f"{catalog}.{silver_schema}.invoice")
        fact_sales_logger.info(f"Successfully read invoice data with {invoice_df.count()} rows")
    except AnalysisException as e:
        fact_sales_logger.error(f"Error reading invoice data: {str(e)}")
        return None, None
    
    # Read invoice_line data
    try:
        invoice_line_df = spark.table(f"{catalog}.{silver_schema}.invoice_line")
        fact_sales_logger.info(f"Successfully read invoice_line data with {invoice_line_df.count()} rows")
    except AnalysisException as e:
        fact_sales_logger.error(f"Error reading invoice_line data: {str(e)}")
        return None, None
    
    return invoice_df, invoice_line_df

# Function to create date dimension key
@log_execution(fact_sales_logger)
def create_date_key(df, date_column):
    fact_sales_logger.info(f"Creating date key from column: {date_column}")
    
    # Create date key in format YYYYMMDD
    return df.withColumn(
        "date_key",
        concat(
            year(col(date_column)),
            lpad(month(col(date_column)), 2, "0"),
            lpad(dayofmonth(col(date_column)), 2, "0")
        ).cast("int")
    )

# Function to create fact sales table
@log_execution(fact_sales_logger)
def create_fact_sales(invoice_df, invoice_line_df):
    fact_sales_logger.info("Creating fact_sales table")
    
    # Add date key to invoice dataframe
    if "invoice_date" in invoice_df.columns:
        invoice_df = create_date_key(invoice_df, "invoice_date")
    elif "created_timestamp" in invoice_df.columns:
        invoice_df = create_date_key(invoice_df, "created_timestamp")
    
    # Join invoice and invoice_line tables
    joined_df = invoice_line_df.join(
        invoice_df,
        invoice_line_df.invoice_id == invoice_df.invoice_id,
        "inner"
    )
    
    # Select columns for fact table
    fact_sales = joined_df.select(
        # Keys
        invoice_df.invoice_id,
        invoice_line_df.invoice_item_id,
        invoice_line_df.item_id,
        invoice_line_df.customer_id,
        invoice_line_df.site_id,
        invoice_line_df.tax_id,
        invoice_line_df.discount_id,
        
        # Date dimensions
        invoice_df.date_key.alias("invoice_date_key"),
        invoice_df.invoice_year,
        invoice_df.invoice_month,
        invoice_df.invoice_quarter,
        
        # Invoice attributes
        invoice_df.invoice_status,
        invoice_df.invoice_type,
        invoice_df.payment_method,
        invoice_df.channel_type,
        invoice_df.reference_number,
        
        # Measures
        invoice_line_df.quantity,
        invoice_line_df.unit_price,
        invoice_line_df.net_amount,
        invoice_line_df.line_discount_amount,
        invoice_line_df.line_tax_amount,
        invoice_line_df.line_total_amount,
        
        # Data quality
        invoice_line_df.data_quality_score.alias("line_quality_score"),
        invoice_df.data_quality_score.alias("invoice_quality_score"),
        
        # Metadata
        current_timestamp().alias("dw_created_at")
    )
    
    # Add overall quality score
    fact_sales = fact_sales.withColumn(
        "data_quality_score",
        (col("line_quality_score") + col("invoice_quality_score")) / 2
    )
    
    return log_dataframe_info(fact_sales, "fact_sales", fact_sales_logger)

# Function to create sales_by_date aggregation
@log_execution(fact_sales_logger)
def create_sales_by_date(fact_sales):
    fact_sales_logger.info("Creating sales_by_date aggregation")
    
    sales_by_date = fact_sales.groupBy(
        "invoice_date_key", "invoice_year", "invoice_month", "invoice_quarter"
    ).agg(
        sql_sum("line_total_amount").alias("total_sales"),
        sql_sum("net_amount").alias("net_sales"),
        sql_sum("line_discount_amount").alias("total_discounts"),
        sql_sum("line_tax_amount").alias("total_taxes"),
        count("invoice_item_id").alias("total_items_sold"),
        count("invoice_id").alias("total_transactions"),
        avg("unit_price").alias("average_unit_price"),
        count("distinct invoice_id").alias("total_invoices"),
        count("distinct customer_id").alias("total_customers"),
        avg("data_quality_score").alias("avg_data_quality_score"),
        current_timestamp().alias("dw_created_at")
    )
    
    # Calculate average order value
    sales_by_date = sales_by_date.withColumn(
        "average_order_value",
        col("total_sales") / col("total_invoices")
    )
    
    return log_dataframe_info(sales_by_date, "sales_by_date", fact_sales_logger)

# Function to create sales_by_product aggregation
@log_execution(fact_sales_logger)
def create_sales_by_product(fact_sales):
    fact_sales_logger.info("Creating sales_by_product aggregation")
    
    sales_by_product = fact_sales.groupBy(
        "item_id", "invoice_year", "invoice_month", "invoice_quarter"
    ).agg(
        sql_sum("line_total_amount").alias("total_sales"),
        sql_sum("net_amount").alias("net_sales"),
        sql_sum("line_discount_amount").alias("total_discounts"),
        sql_sum("line_tax_amount").alias("total_taxes"),
        sql_sum("quantity").alias("total_quantity_sold"),
        count("invoice_id").alias("total_transactions"),
        avg("unit_price").alias("average_unit_price"),
        count("distinct invoice_id").alias("total_invoices"),
        count("distinct customer_id").alias("total_customers"),
        avg("data_quality_score").alias("avg_data_quality_score"),
        current_timestamp().alias("dw_created_at")
    )
    
    return log_dataframe_info(sales_by_product, "sales_by_product", fact_sales_logger)

# Function to write data to gold layer
@log_execution(fact_sales_logger)
def write_to_gold(df, table_name):
    fact_sales_logger.info(f"Writing {table_name} to gold layer")
    target_path = f"{gold_path}/{table_name}"
    
    # Create or replace the table in Unity Catalog
    try:
        # Check if table exists
        spark.sql(f"DESCRIBE TABLE {catalog}.{gold_schema}.{table_name}")
        table_exists = True
    except AnalysisException:
        table_exists = False
    
    # Write data using Delta format
    if table_exists:
        # For fact tables, we typically do a full refresh
        fact_sales_logger.info(f"Overwriting existing table {catalog}.{gold_schema}.{table_name}")
        df.write \
            .format(gold_format) \
            .option("mergeSchema", "true") \
            .mode("overwrite") \
            .saveAsTable(f"{catalog}.{gold_schema}.{table_name}")
    else:
        # Create new table
        fact_sales_logger.info(f"Creating new table {catalog}.{gold_schema}.{table_name}")
        df.write \
            .format(gold_format) \
            .option("mergeSchema", "true") \
            .mode("overwrite") \
            .saveAsTable(f"{catalog}.{gold_schema}.{table_name}")
    
    return df

# Main processing function
@log_execution(fact_sales_logger)
def process_fact_sales():
    fact_sales_logger.info("Processing fact_sales table")
    
    # Read silver data
    invoice_df, invoice_line_df = read_silver_data()
    if invoice_df is None or invoice_line_df is None:
        fact_sales_logger.error("Failed to read silver data")
        return None
    
    # Create fact_sales table
    fact_sales_df = create_fact_sales(invoice_df, invoice_line_df)
    
    # Write fact_sales to gold layer
    write_to_gold(fact_sales_df, "fact_sales")
    
    # Create and write sales_by_date aggregation
    sales_by_date_df = create_sales_by_date(fact_sales_df)
    write_to_gold(sales_by_date_df, "sales_by_date")
    
    # Create and write sales_by_product aggregation
    sales_by_product_df = create_sales_by_product(fact_sales_df)
    write_to_gold(sales_by_product_df, "sales_by_product")
    
    fact_sales_logger.info("Completed fact_sales processing")
    return fact_sales_df

# COMMAND ----------

# Process fact_sales
fact_sales_df = process_fact_sales()
if fact_sales_df is not None:
    log_dataframe_info(fact_sales_df, "fact_sales_final", fact_sales_logger)
    display(fact_sales_df.limit(10))