# Databricks notebook source
# MAGIC %md
# MAGIC # Customer Dimension Table
# MAGIC
# MAGIC This notebook creates and maintains the customer dimension table in the gold layer. It enriches customer data with additional attributes and metrics based on transaction history.

# COMMAND ----------

# MAGIC %run ../../start_up

# COMMAND ----------

from pyspark.sql.functions import (
    current_timestamp, lit, col, datediff, count, sum as sql_sum, 
    avg, min, max, when, concat_ws, upper, expr
)
from pyspark.sql.utils import AnalysisException
from delta.tables import DeltaTable
from pyspark.sql.window import Window

# Use the logger configuration from startup
dim_customer_logger = create_logger(log_file=log_file, component_log_levels=component_log_levels)
dim_customer_logger.info("Initializing notebook")
 
# Extract frequently used config values into variables
catalog = pipeline_config["catalog"]
silver_schema = pipeline_config["schemas"]["silver"]
gold_schema = pipeline_config["schemas"]["gold"]
silver_path = pipeline_config["paths"]["silver_path"]
gold_path = pipeline_config["paths"]["gold_path"]
gold_format = pipeline_config["file_formats"]["gold"]
delta_properties = pipeline_config["delta_properties"]

# Switch to catalog
spark.sql(f"USE CATALOG {catalog}")

# COMMAND ----------

# Function to read data from silver layer
@log_execution(dim_customer_logger)
def read_silver_data():
    dim_customer_logger.info("Reading customer data from silver layer")
    
    # Read customer data
    try:
        customer_df = spark.table(f"{catalog}.{silver_schema}.customer")
        dim_customer_logger.info(f"Successfully read customer data with {customer_df.count()} rows")
    except AnalysisException as e:
        dim_customer_logger.error(f"Error reading customer data: {str(e)}")
        return None, None, None
    
    # Read invoice data if available
    try:
        invoice_df = spark.table(f"{catalog}.{silver_schema}.invoice")
        dim_customer_logger.info(f"Successfully read invoice data with {invoice_df.count()} rows")
    except AnalysisException as e:
        dim_customer_logger.warning(f"Invoice data not available: {str(e)}")
        invoice_df = None
    
    # Read invoice_line data if available
    try:
        invoice_line_df = spark.table(f"{catalog}.{silver_schema}.invoice_line")
        dim_customer_logger.info(f"Successfully read invoice_line data with {invoice_line_df.count()} rows")
    except AnalysisException as e:
        dim_customer_logger.warning(f"Invoice line data not available: {str(e)}")
        invoice_line_df = None
    
    return customer_df, invoice_df, invoice_line_df

# Function to calculate customer metrics
@log_execution(dim_customer_logger)
def calculate_customer_metrics(customer_df, invoice_df, invoice_line_df):
    dim_customer_logger.info("Calculating customer metrics")
    
    if invoice_df is None or invoice_line_df is None:
        dim_customer_logger.warning("Invoice or invoice_line data is missing. Skipping customer metrics calculation.")
        return customer_df
    
    # Join invoice and invoice_line tables
    sales_df = invoice_line_df.join(
        invoice_df,
        invoice_line_df.invoice_id == invoice_df.invoice_id,
        "inner"
    )
    
    # Calculate customer metrics
    customer_metrics = sales_df.groupBy("customer_id").agg(
        sql_sum("line_total_amount").alias("lifetime_value"),
        count("distinct invoice_id").alias("total_orders"),
        avg("line_total_amount").alias("average_order_value"),
        min("invoiceDate").alias("first_purchase_date"),
        max("invoiceDate").alias("last_purchase_date"),
        count("distinct item_id").alias("unique_products_purchased")
    )
    
    # Calculate days since last purchase
    customer_metrics = customer_metrics.withColumn(
        "days_since_last_purchase",
        datediff(current_timestamp(), col("last_purchase_date"))
    )
    
    # Join metrics with customer data
    customer_df = customer_df.join(
        customer_metrics,
        customer_df.customer_id == customer_metrics.customer_id,
        "left"
    ).drop(customer_metrics.customer_id)
    
    # Add customer segments based on RFM (Recency, Frequency, Monetary)
    customer_df = customer_df.withColumn(
        "customer_segment",
        when(col("lifetime_value").isNull(), "New")
        .when(col("days_since_last_purchase") <= 30 & col("lifetime_value") >= 1000, "VIP")
        .when(col("days_since_last_purchase") <= 90 & col("total_orders") >= 3, "Loyal")
        .when(col("days_since_last_purchase") <= 180, "Active")
        .when(col("days_since_last_purchase") > 180 & col("days_since_last_purchase") <= 365, "At Risk")
        .when(col("days_since_last_purchase") > 365, "Inactive")
        .otherwise("Unknown")
    )
    
    return customer_df

# Function to create dimension table
@log_execution(dim_customer_logger)
def create_dimension_table(customer_df, invoice_df, invoice_line_df):
    dim_customer_logger.info("Creating customer dimension table")
    
    # Calculate customer metrics if transaction data is available
    if invoice_df is not None and invoice_line_df is not None:
        customer_df = calculate_customer_metrics(customer_df, invoice_df, invoice_line_df)
    
    # Select relevant columns for dimension table
    columns_to_select = [
        "customer_id", "customer_name", "customer_type", "email", "phone_number", 
        "address", "city", "province", "postal_code", "registration_date", 
        "is_active", "loyalty_card_number", "loyalty_tier", "created_timestamp", 
        "modified_timestamp", "customer_tenure_days", "data_quality_score"
    ]
    
    # Add transaction metrics if available
    transaction_columns = [
        "lifetime_value", "total_orders", "average_order_value", "first_purchase_date",
        "last_purchase_date", "days_since_last_purchase", "unique_products_purchased",
        "customer_segment"
    ]
    
    # Check which columns exist in the dataframe
    available_columns = [col for col in columns_to_select + transaction_columns if col in customer_df.columns]
    
    # Create dimension table with available columns
    dim_df = customer_df.select(*available_columns)
    
    # Add data warehouse timestamp
    dim_df = dim_df.withColumn("dw_created_at", current_timestamp())
    
    return log_dataframe_info(dim_df, "dim_customer", dim_customer_logger)

# Function to write dimension table to gold layer
@log_execution(dim_customer_logger)
def write_dimension_table(df):
    dim_customer_logger.info("Writing customer dimension table to gold layer")
    table_name = "dim_customer"
    target_path = f"{gold_path}/{table_name}"
    
    # Create or replace the table in Unity Catalog
    try:
        # Check if table exists
        spark.sql(f"DESCRIBE TABLE {catalog}.{gold_schema}.{table_name}")
        table_exists = True
    except AnalysisException:
        table_exists = False
    
    # Write data using Delta format
    if table_exists:
        # Merge data using primary key
        dim_customer_logger.info(f"Merging data into existing table {catalog}.{gold_schema}.{table_name}")
        delta_table = DeltaTable.forName(spark, f"{catalog}.{gold_schema}.{table_name}")
        
        # Merge condition based on primary key
        merge_condition = "target.customer_id = source.customer_id"
        
        # Perform merge operation
        delta_table.alias("target").merge(
            df.alias("source"),
            merge_condition
        ).whenMatchedUpdateAll().whenNotMatchedInsertAll().execute()
    else:
        # Create new table
        dim_customer_logger.info(f"Creating new table {catalog}.{gold_schema}.{table_name}")
        df.write \
            .format(gold_format) \
            .option("mergeSchema", "true") \
            .mode("overwrite") \
            .saveAsTable(f"{catalog}.{gold_schema}.{table_name}")
    
    return df

# Main processing function
@log_execution(dim_customer_logger)
def process_dimension_table():
    dim_customer_logger.info("Processing customer dimension table")
    
    # Read silver data
    customer_df, invoice_df, invoice_line_df = read_silver_data()
    if customer_df is None:
        dim_customer_logger.error("Failed to read customer data")
        return None
    
    # Create dimension table
    dim_df = create_dimension_table(customer_df, invoice_df, invoice_line_df)
    
    # Write to gold layer
    final_df = write_dimension_table(dim_df)
    
    dim_customer_logger.info("Completed processing customer dimension table")
    return final_df

# COMMAND ----------

# Process customer dimension table
dim_df = process_dimension_table()
if dim_df is not None:
    log_dataframe_info(dim_df, "dim_customer_final", dim_customer_logger)
    display(dim_df)