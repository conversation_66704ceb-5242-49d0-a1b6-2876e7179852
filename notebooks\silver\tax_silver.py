# Databricks notebook source
# MAGIC %md
# MAGIC # Tax - Bronze to Silver Transformation
# MAGIC
# MAGIC This notebook processes tax data from the bronze layer to the silver layer. It applies data type conversions, handles timestamp formatting, standardizes values, and performs data quality checks.

# COMMAND ----------

# MAGIC %run ../../start_up

# COMMAND ----------

from pyspark.sql.functions import (
    input_file_name, current_timestamp, lit, col, to_timestamp, when, 
    upper, regexp_replace, expr
)
from pyspark.sql.utils import AnalysisException
from delta.tables import DeltaTable

# Use the logger configuration from startup
tax_silver_logger = create_logger(log_file=log_file, component_log_levels=component_log_levels)
tax_silver_logger.info("Initializing notebook")
 
# Extract frequently used config values into variables
catalog = pipeline_config["catalog"]
target_date = pipeline_config["default_processing_date"]
bronze_schema = pipeline_config["schemas"]["bronze"]
silver_schema = pipeline_config["schemas"]["silver"]
bronze_path = pipeline_config["paths"]["bronze_path"]
silver_path = pipeline_config["paths"]["silver_path"]
silver_format = pipeline_config["file_formats"]["silver"]
delta_properties = pipeline_config["delta_properties"]

# Switch to catalog
spark.sql(f"USE CATALOG {catalog}")

# COMMAND ----------

# Table metadata for tax table
table_metadata = {
    "primary_key": "tax_id",
    "timestamp_columns": ["created_timestamp", "modified_timestamp"],
    "numeric_columns": ["tax_rate"],
    "tax_types": ["GST", "HST", "PST", "QST", "VAT", "Sales Tax", "Excise Tax"],
    "tax_statuses": ["Active", "Inactive", "Pending", "Archived"],
    "tax_jurisdictions": ["Federal", "Provincial", "State", "Municipal", "International"]
}

# COMMAND ----------

# Function to read data from bronze layer
@log_execution(tax_silver_logger)
def read_bronze_data(date=None):
    tax_silver_logger.info(f"Reading bronze data for table: tax, date: {date if date else 'all'}") 
    if date:
        # Read specific partition
        df = spark.read.option("mergeSchema", "true").parquet(f"{bronze_path}/tax/ingestion_date={date}")
    else:
        # Read all partitions with schema merging
        df = spark.read.option("mergeSchema", "true").parquet(f"{bronze_path}/tax")
    
    return log_dataframe_info(df, "tax_bronze", tax_silver_logger)

# Function to apply transformations based on table metadata
@log_execution(tax_silver_logger)
def apply_transformations(df):
    tax_silver_logger.info("Applying transformations to tax data")
    
    # Apply timestamp conversions
    for ts_col in table_metadata["timestamp_columns"]:
        if ts_col in df.columns:
            df = df.withColumn(ts_col, to_timestamp(col(ts_col), "M/d/yyyy"))
    
    # Apply numeric conversions
    for num_col in table_metadata["numeric_columns"]:
        if num_col in df.columns:
            df = df.withColumn(num_col, col(num_col).cast("double"))
    
    # Validate tax_rate is between 0-100
    if "tax_rate" in df.columns:
        df = df.withColumn(
            "tax_rate_valid", 
            (col("tax_rate") >= 0) & (col("tax_rate") <= 100)
        )
    
    # Standardize tax_type
    if "tax_type" in df.columns:
        df = df.withColumn("tax_type", upper(col("tax_type")))
        df = df.withColumn(
            "tax_type_valid", 
            col("tax_type").isin([type.upper() for type in table_metadata["tax_types"]])
        )
    
    # Standardize tax_status
    if "tax_status" in df.columns:
        df = df.withColumn("tax_status", upper(col("tax_status")))
        df = df.withColumn(
            "tax_status_valid", 
            col("tax_status").isin([status.upper() for status in table_metadata["tax_statuses"]])
        )
    
    # Standardize tax_jurisdiction
    if "tax_jurisdiction" in df.columns:
        df = df.withColumn("tax_jurisdiction", upper(col("tax_jurisdiction")))
        df = df.withColumn(
            "tax_jurisdiction_valid", 
            col("tax_jurisdiction").isin([jurisdiction.upper() for jurisdiction in table_metadata["tax_jurisdictions"]])
        )
    
    # Add data quality score
    quality_columns = [col for col in df.columns if col.endswith("_valid")]
    if quality_columns:
        # Calculate quality score as percentage of passing validations
        quality_expr = " + ".join([f"CASE WHEN {col} THEN 1 ELSE 0 END" for col in quality_columns])
        df = df.withColumn(
            "data_quality_score",
            expr(f"({quality_expr}) * 100.0 / {len(quality_columns)}")
        )
    else:
        df = df.withColumn("data_quality_score", lit(100.0))
    
    # Add processing timestamp
    df = df.withColumn("processed_at", current_timestamp())
    
    return log_dataframe_info(df, "tax_transformed", tax_silver_logger)

# Function to write data to silver layer
@log_execution(tax_silver_logger)
def write_to_silver(df):
    tax_silver_logger.info("Writing tax data to silver layer")
    table_name = "tax"
    target_path = f"{silver_path}/{table_name}"
    
    # Create or replace the table in Unity Catalog
    try:
        # Check if table exists
        spark.sql(f"DESCRIBE TABLE {catalog}.{silver_schema}.{table_name}")
        table_exists = True
    except AnalysisException:
        table_exists = False
    
    # Write data using Delta format
    if table_exists:
        # Merge data using primary key
        tax_silver_logger.info(f"Merging data into existing table {catalog}.{silver_schema}.{table_name}")
        delta_table = DeltaTable.forName(spark, f"{catalog}.{silver_schema}.{table_name}")
        
        # Merge condition based on primary key
        merge_condition = f"target.{table_metadata['primary_key']} = source.{table_metadata['primary_key']}"
        
        # Perform merge operation
        delta_table.alias("target").merge(
            df.alias("source"),
            merge_condition
        ).whenMatchedUpdateAll().whenNotMatchedInsertAll().execute()
    else:
        # Create new table
        tax_silver_logger.info(f"Creating new table {catalog}.{silver_schema}.{table_name}")
        df.write \
            .format(silver_format) \
            .option("mergeSchema", "true") \
            .mode("overwrite") \
            .saveAsTable(f"{catalog}.{silver_schema}.{table_name}")
    
    return df

# Main processing function
@log_execution(tax_silver_logger)
def process_bronze_to_silver(target_date=None):
    tax_silver_logger.info(f"Processing tax data from bronze to silver for date: {target_date if target_date else 'all'}")
    
    # Read bronze data
    bronze_df = read_bronze_data(target_date)
    if bronze_df is None or bronze_df.isEmpty():
        tax_silver_logger.warning(f"No bronze data found for tax on date {target_date}")
        return None
    
    # Apply transformations
    silver_df = apply_transformations(bronze_df)
    
    # Write to silver layer
    final_df = write_to_silver(silver_df)
    
    tax_silver_logger.info("Completed bronze to silver processing for tax")
    return final_df

# COMMAND ----------

# Process tax data
silver_df = process_bronze_to_silver(target_date)
if silver_df is not None:
    log_dataframe_info(silver_df, "tax_silver_final", tax_silver_logger)
    display(silver_df)