# Databricks notebook source
# MAGIC %md
# MAGIC # Test Gold Layer Data
# MAGIC
# MAGIC This notebook tests the silver layer data availability for gold table creation

# COMMAND ----------

# MAGIC %run ../../start_up

# COMMAND ----------

from pyspark.sql.functions import col, count, sum as spark_sum

# Use the logger configuration from startup
test_logger = create_logger(component_log_levels=component_log_levels)
test_logger.info("🧪 Starting gold layer data test")

# Extract config values
catalog = pipeline_config["catalog"]
target_date = pipeline_config["default_processing_date"]
silver_schema = pipeline_config["schemas"]["silver"]
gold_schema = pipeline_config["schemas"]["gold"]

test_logger.info(f"Target date: {target_date}")
test_logger.info(f"Silver schema: {catalog}.{silver_schema}")

# COMMAND ----------

# MAGIC %md
# MAGIC ## Test 1: Check Silver Tables Availability

# COMMAND ----------

print("🔍 CHECKING SILVER TABLES AVAILABILITY:")
print("=" * 50)

silver_tables = ["invoice_line_cleaned", "item", "product", "cost"]

for table in silver_tables:
    try:
        full_table_name = f"{catalog}.{silver_schema}.{table}"
        df = spark.table(full_table_name)
        row_count = df.count()
        col_count = len(df.columns)
        
        print(f"✅ {table}: {row_count} rows, {col_count} columns")
        
        # Show sample data
        print(f"📋 Sample columns: {df.columns[:5]}...")
        
        # Check for target date data
        if "invoice_date" in df.columns:
            date_filtered = df.filter(col("invoice_date") == target_date)
            date_count = date_filtered.count()
            print(f"📅 Records for {target_date}: {date_count}")
        elif "ingestion_date" in df.columns:
            date_filtered = df.filter(col("ingestion_date") == target_date)
            date_count = date_filtered.count()
            print(f"📅 Records for {target_date}: {date_count}")
        else:
            print(f"📅 No date column found")
            
    except Exception as e:
        print(f"❌ {table}: Failed - {str(e)}")
    
    print("-" * 30)

# COMMAND ----------

# MAGIC %md
# MAGIC ## Test 2: Check Join Relationships

# COMMAND ----------

print("🔗 CHECKING JOIN RELATIONSHIPS:")
print("=" * 50)

try:
    # Read tables
    invoice_line_df = spark.table(f"{catalog}.{silver_schema}.invoice_line_cleaned")
    item_df = spark.table(f"{catalog}.{silver_schema}.item")
    product_df = spark.table(f"{catalog}.{silver_schema}.product")
    cost_df = spark.table(f"{catalog}.{silver_schema}.cost")
    
    print(f"📊 Invoice Line: {invoice_line_df.count()} rows")
    print(f"📊 Item: {item_df.count()} rows")
    print(f"📊 Product: {product_df.count()} rows")
    print(f"📊 Cost: {cost_df.count()} rows")
    
    # Test invoice_line to item join
    print("\n🔗 Testing invoice_line -> item join:")
    if "item_id" in invoice_line_df.columns and "item_id" in item_df.columns:
        joined = invoice_line_df.join(item_df, "item_id", "inner")
        join_count = joined.count()
        print(f"✅ Invoice Line + Item join: {join_count} rows")
    else:
        print("❌ Missing item_id columns for join")
    
    # Test item to product join
    print("\n🔗 Testing item -> product join:")
    if "product_id" in item_df.columns and "product_id" in product_df.columns:
        item_product = item_df.join(product_df, "product_id", "inner")
        join_count = item_product.count()
        print(f"✅ Item + Product join: {join_count} rows")
    else:
        print("❌ Missing product_id columns for join")
        
    # Test full chain join
    print("\n🔗 Testing full chain join:")
    try:
        full_join = invoice_line_df.join(
            item_df.select("item_id", "product_id"), "item_id", "inner"
        ).join(
            product_df, "product_id", "inner"
        )
        full_count = full_join.count()
        print(f"✅ Full chain join: {full_count} rows")
        
        if full_count > 0:
            print("📋 Sample joined data:")
            full_join.select("item_id", "product_id", "product_name", "quantity", "sales_amount").show(5)
        else:
            print("⚠️  No data after full join - check data relationships")
            
    except Exception as e:
        print(f"❌ Full chain join failed: {str(e)}")
        
except Exception as e:
    print(f"❌ Join relationship test failed: {str(e)}")

# COMMAND ----------

# MAGIC %md
# MAGIC ## Test 3: Check Data for Target Date

# COMMAND ----------

print(f"📅 CHECKING DATA FOR TARGET DATE: {target_date}")
print("=" * 50)

try:
    invoice_line_df = spark.table(f"{catalog}.{silver_schema}.invoice_line_cleaned")
    
    # Check if invoice_date column exists
    if "invoice_date" in invoice_line_df.columns:
        # Filter by target date
        date_filtered = invoice_line_df.filter(col("invoice_date") == target_date)
        date_count = date_filtered.count()
        
        print(f"📊 Invoice records for {target_date}: {date_count}")
        
        if date_count > 0:
            print("📋 Sample data for target date:")
            date_filtered.select("invoice_date", "item_id", "quantity", "sales_amount").show(5)
            
            # Check aggregations
            total_sales = date_filtered.agg(spark_sum("sales_amount")).collect()[0][0]
            total_qty = date_filtered.agg(spark_sum("quantity")).collect()[0][0]
            
            print(f"💰 Total sales for {target_date}: {total_sales}")
            print(f"📦 Total quantity for {target_date}: {total_qty}")
        else:
            print(f"⚠️  No invoice data found for {target_date}")
            
            # Show available dates
            print("📅 Available invoice dates:")
            invoice_line_df.select("invoice_date").distinct().orderBy("invoice_date").show(10)
    else:
        print("❌ No invoice_date column found in invoice_line_cleaned")
        print(f"📋 Available columns: {invoice_line_df.columns}")
        
except Exception as e:
    print(f"❌ Date check failed: {str(e)}")

# COMMAND ----------

# MAGIC %md
# MAGIC ## Test 4: Simple Product Performance Calculation

# COMMAND ----------

print("🧮 TESTING SIMPLE PRODUCT PERFORMANCE CALCULATION:")
print("=" * 50)

try:
    # Read tables
    invoice_line_df = spark.table(f"{catalog}.{silver_schema}.invoice_line_cleaned")
    item_df = spark.table(f"{catalog}.{silver_schema}.item")
    product_df = spark.table(f"{catalog}.{silver_schema}.product")
    
    # Simple aggregation by product
    sales_data = invoice_line_df.join(
        item_df.select("item_id", "product_id"), "item_id", "inner"
    )
    
    product_sales = sales_data.groupBy("product_id").agg(
        spark_sum("quantity").alias("total_quantity_sold"),
        spark_sum("sales_amount").alias("total_sales_amount"),
        count("*").alias("transaction_count")
    )
    
    # Join with product details
    final_result = product_sales.join(
        product_df.select("product_id", "product_name", "product_category"), 
        "product_id", 
        "inner"
    )
    
    result_count = final_result.count()
    print(f"✅ Product performance calculation successful: {result_count} products")
    
    if result_count > 0:
        print("📊 Top 10 products by sales:")
        final_result.orderBy(col("total_sales_amount").desc()).show(10)
    else:
        print("⚠️  No product performance data generated")
        
except Exception as e:
    print(f"❌ Product performance calculation failed: {str(e)}")
    import traceback
    traceback.print_exc()

# COMMAND ----------

print("🏁 Gold layer data test complete!")
print("Check the results above to identify any data issues.")
