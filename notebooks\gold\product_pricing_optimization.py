# Databricks notebook source
# MAGIC %md
# MAGIC # Product Pricing Optimization Gold Table
# MAGIC
# MAGIC This notebook creates a gold table for product pricing and margin optimization analysis.
# MAGIC 
# MAGIC **Business Purpose:**
# MAGIC - Compare current selling prices vs actual sales prices
# MAGIC - Analyze margin performance by product
# MAGIC - Identify pricing variance opportunities
# MAGIC - Support data-driven pricing decisions

# COMMAND ----------

# MAGIC %run ../../start_up

# COMMAND ----------

from pyspark.sql.functions import col, avg, sum as spark_sum, count, current_timestamp, lit, when, round as spark_round
from pyspark.sql.types import StructType, StructField, StringType, DoubleType, TimestampType
from delta.tables import DeltaTable
from pyspark.sql.utils import AnalysisException

# Use the logger configuration from startup
pricing_optimization_logger = create_logger(component_log_levels=component_log_levels)
pricing_optimization_logger.info("🎯 Starting product pricing optimization gold table creation")

# Extract frequently used config values into variables
catalog = pipeline_config["catalog"]
target_date = pipeline_config["default_processing_date"]
silver_schema = pipeline_config["schemas"]["silver"]
gold_schema = pipeline_config["schemas"]["gold"]
silver_path = pipeline_config["paths"]["silver_path"]
gold_path = pipeline_config["paths"]["gold_path"]
gold_format = pipeline_config["file_formats"]["gold"]
delta_properties = pipeline_config["delta_properties"]

# COMMAND ----------

# Table metadata for product pricing optimization gold table
table_metadata = {
    "primary_key": "product_id",
    "table_name": "product_pricing_optimization",
    "description": "Product pricing and margin optimization analysis - Essential pricing metrics only",
    "source_tables": ["cost", "item", "product", "invoice_line_cleaned"],
    "metrics": [
        # Essential pricing metrics (12 columns total)
        "product_id",
        "product_name", 
        "category",
        "sub_category",
        "brand",
        "current_selling_price",
        "current_purchase_cost",
        "current_margin_amount",
        "current_margin_percentage",
        "actual_avg_sales_price",
        "price_variance",
        "last_updated"
    ]
}

pricing_optimization_logger.info(f"Table metadata: {table_metadata}")

# COMMAND ----------

# Function to read silver layer data
def read_silver_data():
    pricing_optimization_logger.info("Reading silver layer data for pricing optimization")
    
    try:
        # Read silver tables
        cost_df = spark.table(f"{catalog}.{silver_schema}.cost")
        item_df = spark.table(f"{catalog}.{silver_schema}.item")
        product_df = spark.table(f"{catalog}.{silver_schema}.product")
        invoice_line_df = spark.table(f"{catalog}.{silver_schema}.invoice_line_cleaned")
        
        pricing_optimization_logger.info(f"Cost data: {cost_df.count()} rows")
        pricing_optimization_logger.info(f"Item data: {item_df.count()} rows")
        pricing_optimization_logger.info(f"Product data: {product_df.count()} rows")
        pricing_optimization_logger.info(f"Invoice line data: {invoice_line_df.count()} rows")
        
        return cost_df, item_df, product_df, invoice_line_df
        
    except Exception as e:
        pricing_optimization_logger.error(f"Error reading silver data: {str(e)}")
        raise e

# COMMAND ----------

# Function to calculate product pricing optimization metrics
def calculate_pricing_optimization(cost_df, item_df, product_df, invoice_line_df):
    pricing_optimization_logger.info("Calculating product pricing optimization metrics")
    
    try:
        # Step 1: Join cost data with item data to get product mapping
        cost_item = cost_df.join(
            item_df.select("item_code", "product_code"), 
            "item_code", 
            "inner"
        )
        pricing_optimization_logger.info(f"Cost-Item join: {cost_item.count()} rows")
        
        # Step 2: Join invoice line data with item data
        sales_item = invoice_line_df.join(
            item_df.select("item_code", "product_code"), 
            "item_code", 
            "inner"
        )
        pricing_optimization_logger.info(f"Sales-Item join: {sales_item.count()} rows")
        
        # Step 3: Aggregate cost data by product
        product_cost_metrics = cost_item.groupBy("product_code").agg(
            avg("selling_price").alias("current_selling_price"),
            avg("purchase_cost").alias("current_purchase_cost"),
            avg("margin_amount").alias("current_margin_amount"),
            avg("margin_percentage").alias("current_margin_percentage")
        )
        pricing_optimization_logger.info(f"Product cost metrics: {product_cost_metrics.count()} products")
        
        # Step 4: Aggregate sales data by product (actual sales prices)
        product_sales_metrics = sales_item.groupBy("product_code").agg(
            avg("price").alias("actual_avg_sales_price"),
            count("*").alias("total_transactions"),
            spark_sum("qty").alias("total_quantity_sold")
        )
        pricing_optimization_logger.info(f"Product sales metrics: {product_sales_metrics.count()} products")
        
        # Step 5: Join cost and sales metrics
        pricing_metrics = product_cost_metrics.join(
            product_sales_metrics, 
            "product_code", 
            "inner"
        )
        pricing_optimization_logger.info(f"Combined pricing metrics: {pricing_metrics.count()} products")
        
        # Step 6: Calculate price variance
        pricing_with_variance = pricing_metrics.withColumn(
            "price_variance",
            spark_round(col("actual_avg_sales_price") - col("current_selling_price"), 2)
        )
        
        # Step 7: Join with product master data
        final_pricing_optimization = pricing_with_variance.join(
            product_df.select(
                col("product_code").alias("product_id"),
                col("name").alias("product_name"),
                "category",
                "sub_category",
                "brand"
            ),
            pricing_with_variance.product_code == product_df.product_code,
            "inner"
        ).select(
            "product_id",
            "product_name",
            "category", 
            "sub_category",
            "brand",
            spark_round("current_selling_price", 2).alias("current_selling_price"),
            spark_round("current_purchase_cost", 2).alias("current_purchase_cost"),
            spark_round("current_margin_amount", 2).alias("current_margin_amount"),
            spark_round("current_margin_percentage", 2).alias("current_margin_percentage"),
            spark_round("actual_avg_sales_price", 2).alias("actual_avg_sales_price"),
            "price_variance"
        )
        
        # Step 8: Add metadata columns
        final_result = final_pricing_optimization.withColumn(
            "last_updated", current_timestamp()
        )
        
        pricing_optimization_logger.info(f"Final pricing optimization table: {final_result.count()} products with {len(final_result.columns)} columns")
        
        return final_result
        
    except Exception as e:
        pricing_optimization_logger.error(f"Error calculating pricing optimization: {str(e)}")
        raise e

# COMMAND ----------

# Function to write data to gold layer
def write_to_gold(df):
    pricing_optimization_logger.info("Writing product pricing optimization data to gold layer")
    table_name = table_metadata["table_name"]
    target_path = f"{gold_path}/{table_name}"
    
    try:
        # Check if table exists
        spark.sql(f"DESCRIBE TABLE {catalog}.{gold_schema}.{table_name}")
        table_exists = True
    except AnalysisException:
        table_exists = False
    
    # Write data using Delta format
    if table_exists:
        # Merge data using primary key
        pricing_optimization_logger.info(f"Merging data into existing table {catalog}.{gold_schema}.{table_name}")
        delta_table = DeltaTable.forName(spark, f"{catalog}.{gold_schema}.{table_name}")
        
        # Merge condition based on primary key
        merge_condition = f"target.{table_metadata['primary_key']} = source.{table_metadata['primary_key']}"
        
        # Perform merge operation
        delta_table.alias("target").merge(
            df.alias("source"),
            merge_condition
        ).whenMatchedUpdateAll().whenNotMatchedInsertAll().execute()
        
        pricing_optimization_logger.info("Data merged successfully")
    else:
        # Create new external table
        pricing_optimization_logger.info(f"Creating new external table {catalog}.{gold_schema}.{table_name} at {target_path}")
        df.write \
            .format(gold_format) \
            .option("mergeSchema", "true") \
            .option("path", target_path) \
            .mode("overwrite") \
            .saveAsTable(f"{catalog}.{gold_schema}.{table_name}")
        
        pricing_optimization_logger.info("New table created successfully")
    
    return df

# COMMAND ----------

# Main processing function
def create_pricing_optimization_table(target_date=None):
    pricing_optimization_logger.info(f"Creating product pricing optimization table for date: {target_date if target_date else 'all'}")
    
    try:
        # Read silver data
        cost_df, item_df, product_df, invoice_line_df = read_silver_data()
        
        # Calculate pricing optimization metrics
        pricing_df = calculate_pricing_optimization(cost_df, item_df, product_df, invoice_line_df)
        
        # Write to gold layer
        final_df = write_to_gold(pricing_df)
        
        pricing_optimization_logger.info("Product pricing optimization table creation completed successfully")
        return final_df
        
    except Exception as e:
        pricing_optimization_logger.error(f"Error creating pricing optimization table: {str(e)}")
        raise e

# COMMAND ----------

# Execute the pricing optimization calculation
gold_df = create_pricing_optimization_table(target_date)
if gold_df is not None:
    pricing_optimization_logger.info(f"Pricing optimization table created successfully: {gold_df.count()} rows, {len(gold_df.columns)} columns")
    
    # Show sample results
    print("📊 PRODUCT PRICING OPTIMIZATION RESULTS:")
    print("=" * 60)
    gold_df.show(10, truncate=False)
    
    # Show summary statistics
    print("\n📈 PRICING VARIANCE ANALYSIS:")
    print("-" * 40)
    gold_df.select(
        avg("price_variance").alias("avg_price_variance"),
        count(when(col("price_variance") > 0, 1)).alias("overpriced_products"),
        count(when(col("price_variance") < 0, 1)).alias("underpriced_products"),
        count(when(col("price_variance") == 0, 1)).alias("exact_priced_products")
    ).show()
    
    print("\n💰 TOP 5 PRODUCTS BY MARGIN PERCENTAGE:")
    print("-" * 45)
    gold_df.orderBy(col("current_margin_percentage").desc()).select(
        "product_name", "category", "current_margin_percentage", "price_variance"
    ).show(5, truncate=False)
    
else:
    pricing_optimization_logger.error("Failed to create pricing optimization table")

# COMMAND ----------

pricing_optimization_logger.info("🏁 Product pricing optimization gold table notebook completed")
