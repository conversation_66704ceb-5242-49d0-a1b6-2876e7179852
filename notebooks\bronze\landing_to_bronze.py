# Databricks notebook source
# MAGIC %md
# MAGIC # Landing to Bronze Layer
# MAGIC
# MAGIC This notebook processes data from the landing zone to the bronze layer. It adds metadata columns and performs basic validation.
# MAGIC The code is organized in a modular way with separate functions for each step of the process.

# COMMAND ----------

# MAGIC %run ../../start_up

# COMMAND ----------

from pyspark.sql.functions import current_date, input_file_name, current_timestamp, lit
from pyspark.sql.utils import AnalysisException
 
# Use the logger configuration from startup with log_path from config
landing_to_bronze_logger = create_logger(component_log_levels=component_log_levels)
landing_to_bronze_logger.info("🚀 Initializing landing_to_bronze notebook")

# Extract frequently used config values into variables
catalog = pipeline_config["catalog"]
target_date = pipeline_config["default_processing_date"]
bronze_schema = pipeline_config["schemas"]["bronze"]
landing_path = pipeline_config["paths"]["landing_path"]
bronze_path = pipeline_config["paths"]["bronze_path"]
bronze_format = pipeline_config["file_formats"]["bronze"]  # Using format from config

# Switch to catalog/schema
spark.sql(f"USE CATALOG {catalog}")
spark.sql(f"USE SCHEMA {bronze_schema}")

landing_to_bronze_logger.info(f"Configuration initialized with catalog: {catalog}, schema: {bronze_schema}, format: {bronze_format}")

# COMMAND ----------

@log_execution(landing_to_bronze_logger)
def read_landing_data(table_name, processing_date, source_path=None):
    """Read data from landing zone and add metadata columns.
    
    Args:
        table_name: Name of the table to read
        processing_date: Date to process (used for partitioning)
        source_path: Path to the source file (optional, will be generated if not provided)
        
    Returns:
        DataFrame with source data and metadata columns
    """
    landing_to_bronze_logger.log_start(f"reading {table_name} data", "data_loading")
    
    # Generate source path if not provided
    if source_path is None:
        # Make sure processing_date is a string before calling replace
        if processing_date is None:
            processing_date = current_date().cast("string").collect()[0][0]
        elif not isinstance(processing_date, str):
            processing_date = str(processing_date)
        source_path = f"{landing_path}/{processing_date.replace('-', '/')}/{table_name}.csv"
    
    try:
        # Read source data using configuration
        df = (
            spark.read.format("csv")
            .option("header", "true")
            .option("inferSchema", "false")
            .load(source_path)
            .withColumn("ingested_at", current_timestamp())
            .withColumn("ingestion_date", lit(processing_date))  # for partitioning
        )
        
        # Log DataFrame info
        log_dataframe_info(df, f"{table_name}_source", landing_to_bronze_logger, "data_loading")
        # landing_to_bronze_logger.log_end(f"reading {table_name} data", "data_loading")
        return df
    except Exception as e:
        landing_to_bronze_logger.error(f"Error reading {table_name} data: {str(e)}")
        raise


# COMMAND ----------

@log_execution(landing_to_bronze_logger)
def write_to_bronze(df, table_name, target_path=None):
    """Write data to bronze layer with schema evolution support.
    
    Args:
        df: DataFrame to write
        table_name: Name of the table
        target_path: Path to write the data to (optional, will be generated if not provided)
        
    Returns:
        The input DataFrame (for chaining)
    """
    landing_to_bronze_logger.log_start(f"writing {table_name} data to bronze", "data_writing")
    
    # Generate target path if not provided
    if target_path is None:
        target_path = f"{bronze_path}/{table_name}/{current_date().cast("string").collect()[0][0]}"
    
    try:
        # Set dynamic partition overwrite mode
        spark.conf.set("spark.sql.sources.partitionOverwriteMode", "dynamic")
        
        # Write to bronze layer with schema evolution support and create table directly
        df.write \
            .format(bronze_format) \
            .mode("overwrite") \
            .option("path", target_path) \
            .saveAsTable(f"{catalog}.{bronze_schema}.{table_name}")
            
        landing_to_bronze_logger.info(f"Table {catalog}.{bronze_schema}.{table_name} created or updated")
        # landing_to_bronze_logger.log_end(f"writing {table_name} data to bronze", "data_writing")
        return df
    except Exception as e:
        landing_to_bronze_logger.error(f"Error writing {table_name} data to bronze: {str(e)}")
        raise


# COMMAND ----------

@log_execution(landing_to_bronze_logger)
def process_table(table_name, processing_date=None):
    """Process a single table from landing to bronze.
    
    This method orchestrates the entire process of reading data from landing
    and writing it to bronze while creating the table directly.
    
    Args:
        table_name: Name of the table to process
        processing_date: Date to process (if None, uses current date)
        
    Returns:
        DataFrame with processed data or None if an error occurred
    """
    landing_to_bronze_logger.log_start(f"processing table {table_name}")
    
    # Use current_date() if no date is provided
    if processing_date is None:
        processing_date = current_date().cast("string").collect()[0][0]
        landing_to_bronze_logger.info(f"Using current date: {processing_date}")
    
    # Make sure processing_date is a string before calling replace
    if not isinstance(processing_date, str):
        processing_date = str(processing_date)
    source_path = f"{landing_path}/{table_name}/{processing_date.replace('-', '/')}/{table_name}.csv"
    target_path = f"{bronze_path}/{table_name}/{processing_date.replace('-', '/')}"
    
    try:
        # Step 1: Read data from landing zone
        df = read_landing_data(table_name, processing_date, source_path)
        
        # Step 2: Write data to bronze layer and create table
        df = write_to_bronze(df, table_name, target_path)
        
        # landing_to_bronze_logger.log_end(f"processing table {table_name}")
        return df
    except Exception as e:
        landing_to_bronze_logger.error(f"Error processing {table_name}: {str(e)}")
        return None

# COMMAND ----------

# Get tables to process from pipeline configuration
tables_to_process = pipeline_config["tables_to_process"]
landing_to_bronze_logger.info(f"Using tables from pipeline configuration: {tables_to_process}")

# Main execution
landing_to_bronze_logger.info(f"Starting landing to bronze processing for tables: {tables_to_process}")

# Process each table
for table in tables_to_process:
    # Process the table using the process_table function
    df = process_table(table, target_date)
    
    if df is not None:
        log_dataframe_info(df, f"{table}_bronze", landing_to_bronze_logger)
        display(df)
    else:
        landing_to_bronze_logger.warning(f"Failed to process table {table}")

landing_to_bronze_logger.info("✅ Completed landing to bronze processing")