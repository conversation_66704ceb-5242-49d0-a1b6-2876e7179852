# Databricks notebook source
# MAGIC %md
# MAGIC # Landing to Bronze Layer
# MAGIC
# MAGIC This notebook processes data from the landing zone to the bronze layer. It adds metadata columns and performs basic validation.
# MAGIC The code is organized in a modular way with separate functions for each step of the process.

# COMMAND ----------

# MAGIC %run ../../start_up

# COMMAND ----------

from pyspark.sql.functions import current_date, input_file_name, current_timestamp, lit
from pyspark.sql.utils import AnalysisException
 
# Use the logger configuration from startup with log_path from config
landing_to_bronze_logger = create_logger(component_log_levels=component_log_levels)
landing_to_bronze_logger.info("🚀 Initializing landing_to_bronze notebook")

# Extract frequently used config values into variables
catalog = pipeline_config["catalog"]
target_date = pipeline_config["default_processing_date"]
bronze_schema = pipeline_config["schemas"]["bronze"]
landing_path = pipeline_config["paths"]["landing_path"]
bronze_path = pipeline_config["paths"]["bronze_path"]
bronze_format = pipeline_config["file_formats"]["bronze"]  # Using format from config

# Switch to catalog/schema
spark.sql(f"USE CATALOG {catalog}")
spark.sql(f"USE SCHEMA {bronze_schema}")

landing_to_bronze_logger.info(f"Configuration initialized with catalog: {catalog}, schema: {bronze_schema}, format: {bronze_format}")

# COMMAND ----------

@log_execution(landing_to_bronze_logger)
def read_landing_data(table_name, processing_date, source_path=None):
    """Read data from landing zone and add metadata columns.

    Args:
        table_name: Name of the table to read
        processing_date: Date to process (used for partitioning)
        source_path: Path to the source file (optional, will be generated if not provided)

    Returns:
        DataFrame with source data and metadata columns
    """
    landing_to_bronze_logger.log_start(f"reading {table_name} data", "data_loading")

    # Generate source path if not provided
    if source_path is None:
        # Make sure processing_date is a string before calling replace
        if processing_date is None:
            processing_date = current_date().cast("string").collect()[0][0]
        elif not isinstance(processing_date, str):
            processing_date = str(processing_date)

        # Parse date to get year/month/day for ADLS path structure
        # Expected format: YYYY-MM-DD
        date_parts = processing_date.split('-')
        if len(date_parts) == 3:
            year, month, day = date_parts
        else:
            # Fallback to current date if format is unexpected
            from datetime import datetime
            current = datetime.now()
            year, month, day = str(current.year), f"{current.month:02d}", f"{current.day:02d}"

        # ADLS path structure: landing_path/table_name/year/month/day/table_name.csv
        source_path = f"{landing_path}/{table_name}/{year}/{month}/{day}/{table_name}.csv"

    landing_to_bronze_logger.info(f"Reading from source path: {source_path}")

    try:
        # Check if file exists first
        try:
            file_info = dbutils.fs.ls(source_path)
            landing_to_bronze_logger.info(f"Found file: {source_path}")
        except Exception:
            landing_to_bronze_logger.error(f"File not found: {source_path}")
            raise FileNotFoundError(f"Source file not found: {source_path}")

        # Read source data using configuration
        df = (
            spark.read.format("csv")
            .option("header", "true")
            .option("inferSchema", "false")
            .load(source_path)
            .withColumn("source_file_path", input_file_name())
            .withColumn("ingestion_timestamp", current_timestamp())
            .withColumn("ingestion_date", lit(processing_date))  # for partitioning
        )

        # Log DataFrame info
        log_dataframe_info(df, f"{table_name}_source", landing_to_bronze_logger)
        return df
    except Exception as e:
        landing_to_bronze_logger.error(f"Error reading {table_name} data: {str(e)}")
        raise


# COMMAND ----------

@log_execution(landing_to_bronze_logger)
def write_to_bronze(df, table_name, processing_date):
    """Write data to bronze layer with partitioning support.

    Args:
        df: DataFrame to write
        table_name: Name of the table
        processing_date: Date for partitioning

    Returns:
        The input DataFrame (for chaining)
    """
    landing_to_bronze_logger.log_start(f"writing {table_name} data to bronze", "data_writing")

    # Generate target path with partitioning
    target_path = f"{bronze_path}/{table_name}"

    try:
        # Check if table exists
        table_exists = False
        try:
            spark.sql(f"DESCRIBE TABLE {catalog}.{bronze_schema}.{table_name}")
            table_exists = True
            landing_to_bronze_logger.info(f"Table {catalog}.{bronze_schema}.{table_name} exists")
        except:
            landing_to_bronze_logger.info(f"Table {catalog}.{bronze_schema}.{table_name} does not exist, will create")

        if table_exists:
            # Append to existing table with partitioning
            df.write \
                .format(bronze_format) \
                .mode("append") \
                .option("mergeSchema", "true") \
                .partitionBy("ingestion_date") \
                .saveAsTable(f"{catalog}.{bronze_schema}.{table_name}")
        else:
            # Create new table with partitioning
            df.write \
                .format(bronze_format) \
                .mode("overwrite") \
                .option("mergeSchema", "true") \
                .option("path", target_path) \
                .partitionBy("ingestion_date") \
                .saveAsTable(f"{catalog}.{bronze_schema}.{table_name}")

        landing_to_bronze_logger.info(f"Successfully wrote {df.count()} records to {catalog}.{bronze_schema}.{table_name}")
        return df
    except Exception as e:
        landing_to_bronze_logger.error(f"Error writing {table_name} data to bronze: {str(e)}")
        raise


# COMMAND ----------

@log_execution(landing_to_bronze_logger)
def process_table(table_name, processing_date=None):
    """Process a single table from landing to bronze.

    This method orchestrates the entire process of reading data from landing
    and writing it to bronze while creating the table directly.

    Args:
        table_name: Name of the table to process
        processing_date: Date to process (if None, uses current date)

    Returns:
        DataFrame with processed data or None if an error occurred
    """
    landing_to_bronze_logger.log_start(f"processing table {table_name}")

    # Use current_date() if no date is provided
    if processing_date is None:
        processing_date = current_date().cast("string").collect()[0][0]
        landing_to_bronze_logger.info(f"Using current date: {processing_date}")

    # Make sure processing_date is a string
    if not isinstance(processing_date, str):
        processing_date = str(processing_date)

    try:
        # Step 1: Read data from landing zone (simplified path structure)
        df = read_landing_data(table_name, processing_date)

        # Step 2: Write data to bronze layer and create table
        df = write_to_bronze(df, table_name, processing_date)

        landing_to_bronze_logger.info(f"Successfully processed table {table_name}")
        return df
    except Exception as e:
        landing_to_bronze_logger.error(f"Error processing {table_name}: {str(e)}")
        return None

# COMMAND ----------

# Get tables to process from pipeline configuration
tables_to_process = pipeline_config["tables_to_process"]
landing_to_bronze_logger.info(f"Using tables from pipeline configuration: {tables_to_process}")

# Main execution
landing_to_bronze_logger.info(f"Starting landing to bronze processing for tables: {tables_to_process}")

# Process each table
for table in tables_to_process:
    # Process the table using the process_table function
    df = process_table(table, target_date)
    
    if df is not None:
        log_dataframe_info(df, f"{table}_bronze", landing_to_bronze_logger)
        display(df)
    else:
        landing_to_bronze_logger.warning(f"Failed to process table {table}")

landing_to_bronze_logger.info("✅ Completed landing to bronze processing")