# Databricks notebook source
# MAGIC %md
# MAGIC # Bronze Reader Utilities
# MAGIC
# MAGIC Utility functions for reading bronze data with correct path structure

# COMMAND ----------

# MAGIC %run ../utils/logger

# COMMAND ----------

from pyspark.sql.functions import col

# Create logger for this utility
bronze_reader_logger = create_logger(component_log_levels={"data_loading": "INFO"})

# COMMAND ----------

def read_bronze_data_with_date_structure(spark, bronze_path, table_name, bronze_format, date=None, logger=None):
    """
    Read bronze data with the correct date folder structure.
    
    Args:
        spark: Spark session
        bronze_path: Base bronze path
        table_name: Name of the table
        bronze_format: Format of bronze files (parquet, delta, etc.)
        date: Date in YYYY-MM-DD format (optional)
        logger: Logger instance (optional)
        
    Returns:
        DataFrame with bronze data
    """
    if logger is None:
        logger = bronze_reader_logger
        
    logger.info(f"Reading bronze data for table: {table_name}, date: {date if date else 'all'}")
    
    if date:
        # Parse date to get year/month/day for bronze path structure
        date_parts = date.split('-')
        if len(date_parts) == 3:
            year, month, day = date_parts
            # Bronze uses date folder structure: /bronze/table_name/2025/05/31/
            bronze_date_path = f"{bronze_path}/{table_name}/{year}/{month}/{day}"
            logger.info(f"Reading from bronze date path: {bronze_date_path}")
            
            try:
                # Try to read from the date-partitioned path
                df = spark.read.option("mergeSchema", "true").format(bronze_format).load(bronze_date_path)
                logger.info(f"Successfully read from date path: {bronze_date_path}")
                return df
            except Exception as e:
                logger.warning(f"Could not read from date path {bronze_date_path}: {str(e)}")
                # Fallback: try to read from table-level path and filter by date
                try:
                    df = spark.read.option("mergeSchema", "true").format(bronze_format).load(f"{bronze_path}/{table_name}")
                    df = df.filter(col("ingestion_date") == date)
                    logger.info(f"Successfully read from table path with date filter")
                    return df
                except Exception as e2:
                    logger.error(f"Could not read from table path either: {str(e2)}")
                    raise e2
        else:
            logger.warning(f"Invalid date format: {date}, reading all data")
            df = spark.read.option("mergeSchema", "true").format(bronze_format).load(f"{bronze_path}/{table_name}")
    else:
        # Read all partitions with schema merging
        logger.info(f"Reading all bronze data from: {bronze_path}/{table_name}")
        df = spark.read.option("mergeSchema", "true").format(bronze_format).load(f"{bronze_path}/{table_name}")
    
    return df

# COMMAND ----------

def get_bronze_table_path(bronze_path, table_name, date=None):
    """
    Get the correct bronze table path based on date structure.
    
    Args:
        bronze_path: Base bronze path
        table_name: Name of the table
        date: Date in YYYY-MM-DD format (optional)
        
    Returns:
        String path to bronze data
    """
    if date:
        date_parts = date.split('-')
        if len(date_parts) == 3:
            year, month, day = date_parts
            return f"{bronze_path}/{table_name}/{year}/{month}/{day}"
    
    return f"{bronze_path}/{table_name}"

# COMMAND ----------

def check_bronze_data_exists(spark, bronze_path, table_name, date=None, logger=None):
    """
    Check if bronze data exists for the given table and date.
    
    Args:
        spark: Spark session
        bronze_path: Base bronze path
        table_name: Name of the table
        date: Date in YYYY-MM-DD format (optional)
        logger: Logger instance (optional)
        
    Returns:
        Boolean indicating if data exists
    """
    if logger is None:
        logger = bronze_reader_logger
        
    try:
        path = get_bronze_table_path(bronze_path, table_name, date)
        # Try to list files in the path
        files = spark._jvm.org.apache.hadoop.fs.FileSystem.get(
            spark._jsc.hadoopConfiguration()
        ).listStatus(
            spark._jvm.org.apache.hadoop.fs.Path(path)
        )
        
        if len(files) > 0:
            logger.info(f"Bronze data exists at: {path}")
            return True
        else:
            logger.warning(f"No files found at: {path}")
            return False
            
    except Exception as e:
        logger.warning(f"Bronze data does not exist at: {path} - {str(e)}")
        return False
