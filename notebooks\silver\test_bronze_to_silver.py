# Databricks notebook source
# MAGIC %md
# MAGIC # Test Bronze to Silver Reading
# MAGIC
# MAGIC This notebook tests the bronze to silver reading with the correct path structure

# COMMAND ----------

# MAGIC %run ../../start_up

# COMMAND ----------

# MAGIC %run ../../utils/bronze_reader_utils

# COMMAND ----------

from pyspark.sql.functions import col

# Use the logger configuration from startup
test_logger = create_logger(component_log_levels=component_log_levels)
test_logger.info("🧪 Starting bronze to silver reading test")

# Extract config values
catalog = pipeline_config["catalog"]
target_date = pipeline_config["default_processing_date"]
bronze_schema = pipeline_config["schemas"]["bronze"]
silver_schema = pipeline_config["schemas"]["silver"]
bronze_path = pipeline_config["paths"]["bronze_path"]
bronze_format = pipeline_config["file_formats"]["bronze"]

test_logger.info(f"Config - Target date: {target_date}")
test_logger.info(f"Bronze path: {bronze_path}")
test_logger.info(f"Bronze format: {bronze_format}")

# COMMAND ----------

# MAGIC %md
# MAGIC ## Test 1: Check Bronze Data Structure

# COMMAND ----------

tables_to_test = ["item", "product", "cost", "invoice_line"]

for table in tables_to_test:
    print(f"\n🔍 Testing {table} table:")
    print("=" * 40)
    
    try:
        # Test with date
        test_logger.info(f"Testing {table} with date {target_date}")
        df = read_bronze_data_with_date_structure(
            spark=spark,
            bronze_path=bronze_path,
            table_name=table,
            bronze_format=bronze_format,
            date=target_date,
            logger=test_logger
        )
        
        print(f"✅ Successfully read {table} data")
        print(f"📊 Shape: {df.count()} rows, {len(df.columns)} columns")
        print(f"📋 Columns: {df.columns[:10]}...")  # Show first 10 columns
        
        # Show sample data
        print("📄 Sample data:")
        df.show(3, truncate=False)
        
    except Exception as e:
        print(f"❌ Failed to read {table}: {str(e)}")
        test_logger.error(f"Failed to read {table}: {str(e)}")

# COMMAND ----------

# MAGIC %md
# MAGIC ## Test 2: Test Reading Without Date (All Data)

# COMMAND ----------

print("🔍 Testing reading all data (no date filter):")
print("=" * 50)

for table in tables_to_test:
    try:
        test_logger.info(f"Testing {table} without date filter")
        df = read_bronze_data_with_date_structure(
            spark=spark,
            bronze_path=bronze_path,
            table_name=table,
            bronze_format=bronze_format,
            date=None,
            logger=test_logger
        )
        
        print(f"✅ {table}: {df.count()} total rows")
        
    except Exception as e:
        print(f"❌ {table}: Failed - {str(e)}")

# COMMAND ----------

# MAGIC %md
# MAGIC ## Test 3: Test Specific Item Silver Processing

# COMMAND ----------

print("🧪 Testing item silver processing specifically:")
print("=" * 50)

try:
    # Read item bronze data
    item_df = read_bronze_data_with_date_structure(
        spark=spark,
        bronze_path=bronze_path,
        table_name="item",
        bronze_format=bronze_format,
        date=target_date,
        logger=test_logger
    )
    
    print(f"✅ Item bronze data loaded: {item_df.count()} rows")
    
    # Check if we have the expected columns for transformation
    expected_bronze_cols = ["item_code", "product_code", "name", "uom", "pack_size"]
    missing_cols = [col for col in expected_bronze_cols if col not in item_df.columns]
    
    if missing_cols:
        print(f"⚠️  Missing expected columns: {missing_cols}")
    else:
        print("✅ All expected bronze columns present")
    
    # Show the actual columns
    print(f"📋 Actual columns: {item_df.columns}")
    
    # Test if we can apply basic transformations
    print("\n🔄 Testing basic transformation...")
    
    # Simple column rename test
    transformed_df = item_df.withColumnRenamed("item_code", "item_id")
    print(f"✅ Basic transformation successful")
    print(f"📋 New columns: {transformed_df.columns[:10]}...")
    
except Exception as e:
    print(f"❌ Item processing test failed: {str(e)}")
    test_logger.error(f"Item processing test failed: {str(e)}")
    import traceback
    traceback.print_exc()

# COMMAND ----------

# MAGIC %md
# MAGIC ## Test 4: Check Bronze Table Paths

# COMMAND ----------

print("🔍 Checking bronze table paths:")
print("=" * 40)

for table in tables_to_test:
    # Test date-specific path
    date_path = get_bronze_table_path(bronze_path, table, target_date)
    print(f"📁 {table} (with date): {date_path}")
    
    # Test general path
    general_path = get_bronze_table_path(bronze_path, table, None)
    print(f"📁 {table} (general): {general_path}")
    
    # Check if data exists
    exists = check_bronze_data_exists(spark, bronze_path, table, target_date, test_logger)
    print(f"📊 Data exists: {exists}")
    print("-" * 30)

# COMMAND ----------

test_logger.info("🏁 Bronze to silver reading test complete")
print("🏁 Test complete! Check the results above.")
