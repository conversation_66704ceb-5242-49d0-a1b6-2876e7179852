# Databricks notebook source
# MAGIC %md
# MAGIC # Product - Bronze to Silver Transformation
# MAGIC
# MAGIC This notebook processes product data from the bronze layer to the silver layer. It applies data type conversions, handles timestamp formatting, and performs data quality checks.

# COMMAND ----------

# MAGIC %run ../../start_up

# COMMAND ----------

from pyspark.sql.functions import input_file_name, current_timestamp, lit, col, to_timestamp, when
from pyspark.sql.utils import AnalysisException
from delta.tables import DeltaTable

# Use the logger configuration from startup
product_silver_logger = create_logger(log_file=log_file, component_log_levels=component_log_levels)
product_silver_logger.info("Initializing notebook")
 
# Extract frequently used config values into variables
catalog = pipeline_config["catalog"]
target_date = pipeline_config["default_processing_date"]
bronze_schema = pipeline_config["schemas"]["bronze"]
silver_schema = pipeline_config["schemas"]["silver"]
bronze_path = pipeline_config["paths"]["bronze_path"]
silver_path = pipeline_config["paths"]["silver_path"]
silver_format = pipeline_config["file_formats"]["silver"]
delta_properties = pipeline_config["delta_properties"]

# Switch to catalog
spark.sql(f"USE CATALOG {catalog}")

# COMMAND ----------

# Table metadata for product table
table_metadata = {
    "primary_key": "product_id",
    "timestamp_columns": ["created_at", "updated_at"],
    "numeric_columns": ["price", "stock_quantity"]
}

# COMMAND ----------

# Function to read data from bronze layer
@log_execution(product_silver_logger)
def read_bronze_data(date=None):
    product_silver_logger.info(f"Reading bronze data for table: product, date: {date if date else 'all'}") 
    if date:
        # Read specific partition
        df = spark.read.option("mergeSchema", "true").parquet(f"{bronze_path}/product/ingestion_date={date}")
    else:
        # Read all partitions with schema merging
        df = spark.read.option("mergeSchema", "true").parquet(f"{bronze_path}/product")
    
    return log_dataframe_info(df, "product_bronze", product_silver_logger)

# Function to apply transformations based on table metadata
@log_execution(product_silver_logger)
def apply_transformations(df):
    product_silver_logger.info("Applying transformations to product data")
    
    # Apply timestamp conversions
    for ts_col in table_metadata["timestamp_columns"]:
        if ts_col in df.columns:
            df = df.withColumn(ts_col, to_timestamp(col(ts_col)))
    
    # Apply numeric conversions
    for num_col in table_metadata["numeric_columns"]:
        if num_col in df.columns:
            df = df.withColumn(num_col, col(num_col).cast("double"))
    
    # Add processing timestamp
    df = df.withColumn("processed_at", current_timestamp())
    
    return log_dataframe_info(df, "product_transformed", product_silver_logger)

# Function to write data to silver layer
@log_execution(product_silver_logger)
def write_to_silver(df):
    product_silver_logger.info("Writing product data to silver layer")
    table_name = "product"
    target_path = f"{silver_path}/{table_name}"
    
    # Create or replace the table in Unity Catalog
    try:
        # Check if table exists
        spark.sql(f"DESCRIBE TABLE {catalog}.{silver_schema}.{table_name}")
        table_exists = True
    except AnalysisException:
        table_exists = False
    
    # Write data using Delta format
    if table_exists:
        # Merge data using primary key
        product_silver_logger.info(f"Merging data into existing table {catalog}.{silver_schema}.{table_name}")
        delta_table = DeltaTable.forName(spark, f"{catalog}.{silver_schema}.{table_name}")
        
        # Merge condition based on primary key
        merge_condition = f"target.{table_metadata['primary_key']} = source.{table_metadata['primary_key']}"
        
        # Perform merge operation
        delta_table.alias("target").merge(
            df.alias("source"),
            merge_condition
        ).whenMatchedUpdateAll().whenNotMatchedInsertAll().execute()
    else:
        # Create new table
        product_silver_logger.info(f"Creating new table {catalog}.{silver_schema}.{table_name}")
        df.write \
            .format(silver_format) \
            .option("mergeSchema", "true") \
            .mode("overwrite") \
            .saveAsTable(f"{catalog}.{silver_schema}.{table_name}")
    
    return df

# Main processing function
@log_execution(product_silver_logger)
def process_bronze_to_silver(target_date=None):
    product_silver_logger.info(f"Processing product data from bronze to silver for date: {target_date if target_date else 'all'}")
    
    # Read bronze data
    bronze_df = read_bronze_data(target_date)
    if bronze_df is None or bronze_df.isEmpty():
        product_silver_logger.warning(f"No bronze data found for product on date {target_date}")
        return None
    
    # Apply transformations
    silver_df = apply_transformations(bronze_df)
    
    # Write to silver layer
    final_df = write_to_silver(silver_df)
    
    product_silver_logger.info("Completed bronze to silver processing for product")
    return final_df

# COMMAND ----------

# Process product data
silver_df = process_bronze_to_silver(target_date)
if silver_df is not None:
    log_dataframe_info(silver_df, "product_silver_final", product_silver_logger)
    display(silver_df)