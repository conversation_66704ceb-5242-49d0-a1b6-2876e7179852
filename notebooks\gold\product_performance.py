# Databricks notebook source
# MAGIC %md
# MAGIC # Product Performance - Gold Layer
# MAGIC
# MAGIC This notebook creates the product performance gold table by aggregating data from silver layer tables:
# MAGIC - invoice_line (sales transactions)
# MAGIC - item (product items)
# MAGIC - product (product master data)
# MAGIC - cost (cost and pricing information)
# MAGIC
# MAGIC The resulting table provides key performance metrics for products including:
# MAGIC - Total quantity sold
# MAGIC - Total sales revenue
# MAGIC - Top selling item per product
# MAGIC - Product attributes (category, brand, perishability)

# COMMAND ----------

# MAGIC %run ../../start_up

# COMMAND ----------

# MAGIC %run ../../utils/silver_utils

# COMMAND ----------

# MAGIC %run ../../utils/schema_utils

# COMMAND ----------

from pyspark.sql.functions import (
    col, sum as spark_sum, count, max as spark_max, min as spark_min,
    desc, asc, row_number, first, when, isnan, isnull,
    current_timestamp, lit, coalesce, round as spark_round
)
from pyspark.sql.window import Window
from pyspark.sql.utils import AnalysisException
from delta.tables import DeltaTable

# Use the logger configuration from startup
product_performance_logger = create_logger(component_log_levels=component_log_levels)
product_performance_logger.info("Initializing product performance gold notebook")

# Extract frequently used config values into variables
catalog = pipeline_config["catalog"]
target_date = pipeline_config["default_processing_date"]
silver_schema = pipeline_config["schemas"]["silver"]
gold_schema = pipeline_config["schemas"]["gold"]
gold_path = pipeline_config["paths"]["gold_path"]
gold_format = pipeline_config["file_formats"]["gold"]
delta_properties = pipeline_config["delta_properties"]

# Switch to catalog
spark.sql(f"USE CATALOG {catalog}")

# COMMAND ----------

# Table metadata for product performance gold table
table_metadata = {
    "primary_key": "product_id",
    "table_name": "product_performance",
    "description": "Product performance metrics aggregated from sales data",
    "source_tables": ["invoice_line", "item", "product", "cost"],
    "metrics": [
        "total_quantity_sold",
        "total_sales_amount",
        "top_selling_item_id",
        "top_selling_item_name",
        "avg_selling_price",
        "total_margin_amount"
    ]
}

# COMMAND ----------

# Function to read silver layer data
@log_execution(product_performance_logger)
def read_silver_data():
    product_performance_logger.info("Reading data from silver layer tables")

    try:
        # Read invoice_line data
        invoice_line_df = spark.table(f"{catalog}.{silver_schema}.invoice_line")
        product_performance_logger.info(f"Read {invoice_line_df.count()} records from invoice_line")

        # Read item data
        item_df = spark.table(f"{catalog}.{silver_schema}.item")
        product_performance_logger.info(f"Read {item_df.count()} records from item")

        # Read product data
        product_df = spark.table(f"{catalog}.{silver_schema}.product")
        product_performance_logger.info(f"Read {product_df.count()} records from product")

        # Read cost data
        cost_df = spark.table(f"{catalog}.{silver_schema}.cost")
        product_performance_logger.info(f"Read {cost_df.count()} records from cost")

        return invoice_line_df, item_df, product_df, cost_df

    except AnalysisException as e:
        product_performance_logger.error(f"Error reading silver layer tables: {str(e)}")
        raise e

# COMMAND ----------

# Function to calculate top selling item per product
@log_execution(product_performance_logger)
def calculate_top_selling_items(invoice_line_df, item_df):
    product_performance_logger.info("Calculating top selling items per product")

    # Join invoice_line with item to get product_id
    sales_by_item = invoice_line_df.join(
        item_df.select("item_id", "product_id", "item_name"),
        "item_id",
        "inner"
    )

    # Aggregate sales by product and item
    item_sales = sales_by_item.groupBy("product_id", "item_id", "item_name").agg(
        spark_sum("quantity").alias("total_item_quantity"),
        spark_sum("line_total_amount").alias("total_item_sales")
    )

    # Window function to rank items by quantity sold within each product
    window_spec = Window.partitionBy("product_id").orderBy(desc("total_item_quantity"))

    # Get top selling item per product
    top_items = item_sales.withColumn(
        "rank", row_number().over(window_spec)
    ).filter(col("rank") == 1).select(
        "product_id",
        col("item_id").alias("top_selling_item_id"),
        col("item_name").alias("top_selling_item_name"),
        col("total_item_quantity").alias("top_item_quantity"),
        col("total_item_sales").alias("top_item_sales")
    )

    return log_dataframe_info(top_items, "top_selling_items", product_performance_logger)

# COMMAND ----------

# Function to calculate product performance metrics
@log_execution(product_performance_logger)
def calculate_product_performance(invoice_line_df, item_df, product_df, cost_df):
    product_performance_logger.info("Calculating product performance metrics")

    # Join invoice_line with item to get product_id
    sales_data = invoice_line_df.join(
        item_df.select("item_id", "product_id"),
        "item_id",
        "inner"
    )

    # Aggregate sales metrics by product
    product_sales = sales_data.groupBy("product_id").agg(
        spark_sum("quantity").alias("total_quantity_sold"),
        spark_sum("line_total_amount").alias("total_sales_amount"),
        count("invoice_line_id").alias("total_transactions"),
        spark_round(
            spark_sum("line_total_amount") / spark_sum("quantity"), 2
        ).alias("avg_selling_price")
    )

    # Calculate top selling items
    top_items = calculate_top_selling_items(invoice_line_df, item_df)

    # Join with product master data
    product_performance = product_sales.join(
        product_df.select(
            "product_id",
            "product_name",
            "product_category",
            "product_sub_category",
            "brand_name",
            "is_perishable",
            "is_organic",
            "is_private_label",
            "is_active"
        ),
        "product_id",
        "inner"
    )

    # Join with top selling items
    product_performance = product_performance.join(
        top_items,
        "product_id",
        "left"
    )

    # Calculate margin information from cost data
    # Get average cost information per product (via items)
    item_cost = item_df.select("item_id", "product_id").join(
        cost_df.select(
            "item_id",
            "selling_price",
            "purchase_cost",
            "margin_amount"
        ).filter(col("is_active") == True),
        "item_id",
        "left"
    )

    product_cost = item_cost.groupBy("product_id").agg(
        spark_round(
            spark_sum("margin_amount") / count("item_id"), 2
        ).alias("avg_margin_per_item"),
        spark_round(
            spark_sum("selling_price") / count("item_id"), 2
        ).alias("avg_item_price"),
        spark_round(
            spark_sum("purchase_cost") / count("item_id"), 2
        ).alias("avg_item_cost")
    )

    # Join with cost information
    product_performance = product_performance.join(
        product_cost,
        "product_id",
        "left"
    )

    # Calculate total estimated margin
    product_performance = product_performance.withColumn(
        "total_margin_amount",
        spark_round(
            coalesce(col("avg_margin_per_item"), lit(0)) * col("total_quantity_sold"), 2
        )
    )

    # Add data quality and processing metadata
    product_performance = product_performance.withColumn(
        "last_updated", current_timestamp()
    ).withColumn(
        "data_quality_score",
        when(
            col("total_quantity_sold").isNull() |
            col("total_sales_amount").isNull() |
            col("product_name").isNull(),
            lit(50.0)
        ).otherwise(lit(100.0))
    )

    return log_dataframe_info(product_performance, "product_performance_calculated", product_performance_logger)

# COMMAND ----------

# Function to write data to gold layer
@log_execution(product_performance_logger)
def write_to_gold(df):
    product_performance_logger.info("Writing product performance data to gold layer")
    table_name = table_metadata["table_name"]

    # Optional: Validate schema before writing
    try:
        expected_schema = get_schema("product_performance", "gold")
        if expected_schema:
            is_valid, validation_errors = validate_schema(df, expected_schema)
            if not is_valid:
                product_performance_logger.warning(f"Schema validation warnings: {validation_errors}")
            else:
                product_performance_logger.info("Schema validation passed")
    except Exception as e:
        product_performance_logger.warning(f"Schema validation skipped due to error: {str(e)}")

    # Create or replace the table in Unity Catalog
    try:
        # Check if table exists
        spark.sql(f"DESCRIBE TABLE {catalog}.{gold_schema}.{table_name}")
        table_exists = True
    except AnalysisException:
        table_exists = False

    # Write data using Delta format
    if table_exists:
        # Merge data using primary key
        product_performance_logger.info(f"Merging data into existing table {catalog}.{gold_schema}.{table_name}")
        delta_table = DeltaTable.forName(spark, f"{catalog}.{gold_schema}.{table_name}")

        # Merge condition based on primary key
        merge_condition = f"target.{table_metadata['primary_key']} = source.{table_metadata['primary_key']}"

        # Perform merge operation
        delta_table.alias("target").merge(
            df.alias("source"),
            merge_condition
        ).whenMatchedUpdateAll().whenNotMatchedInsertAll().execute()
    else:
        # Create new table
        product_performance_logger.info(f"Creating new table {catalog}.{gold_schema}.{table_name}")
        df.write \
            .format(gold_format) \
            .option("mergeSchema", "true") \
            .mode("overwrite") \
            .saveAsTable(f"{catalog}.{gold_schema}.{table_name}")

    return df

# COMMAND ----------

# Main processing function
@log_execution(product_performance_logger)
def create_product_performance_table(target_date=None):
    product_performance_logger.info(f"Creating product performance gold table for date: {target_date if target_date else 'all'}")

    # Read silver layer data
    invoice_line_df, item_df, product_df, cost_df = read_silver_data()

    # Filter data by date if specified
    if target_date:
        product_performance_logger.info(f"Filtering data for target date: {target_date}")
        invoice_line_df = invoice_line_df.filter(col("invoice_date") == target_date)

    # Calculate product performance metrics
    product_performance_df = calculate_product_performance(
        invoice_line_df, item_df, product_df, cost_df
    )

    # Write to gold layer
    final_df = write_to_gold(product_performance_df)

    product_performance_logger.info("Completed product performance gold table creation")
    return final_df

# COMMAND ----------

# Execute the product performance calculation
gold_df = create_product_performance_table(target_date)
if gold_df is not None:
    log_dataframe_info(gold_df, "product_performance_final", product_performance_logger)
    display(gold_df)
