# Sobeys Data-Driven Business Scenarios and User Stories

Based on analysis of the available data in the CSV files, the following business scenarios and user stories are directly supported by the data structure.

## Scenario 1: Product Category Performance Analysis

**Business Context:**
Sobeys needs to understand which product categories and subcategories are driving profitability and which ones need optimization. The data includes detailed product information and sales data through invoice_line.csv.

**User Story:**
> As the VP of Advanced Analytics at Sobeys, I need to analyze sales and profitability by product category and subcategory, so that we can optimize our assortment to increase overall category profitability by 5% while maintaining customer satisfaction.

**Supported by:**
- product.csv (product_id, category, sub_category, brand, is_perishable)
- item.csv (item_id, product_id)
- cost.csv (item_id, selling_cost, purchase_cost, margin_percentage)
- invoice_line.csv (item_id, quantity, unit_price, line_total_amount)
- invoice.csv (invoice_id, created_timestamp)


## Scenario 2: Store Performance by Region and Format

**Business Context:**
Sobeys operates different store formats across various regions and needs to understand performance variations to optimize operations. The data includes store attributes and sales by location.

**User Story:**
> As the VP of Advanced Analytics at Sobeys, I need to analyze store performance metrics across different formats and regions, so that we can identify underperforming locations and implement targeted improvement strategies to increase same-store sales by 3%.

**Supported by:**
- site.csv (site_id, region_name, site_name, store_type, store_size_sqft)
- invoice_line.csv (site_id, line_total_amount)
- invoice.csv (invoice_id, created_timestamp)


## Scenario 3: Perishable Product Management and Waste Reduction

**Business Context:**
Sobeys sells many perishable products that have limited shelf life and require careful inventory management to minimize waste. The data includes perishability flags and shelf life information.

**User Story:**
> As the VP of Advanced Analytics at Sobeys, I need to analyze sales patterns and shelf life data for perishable products, so that we can implement inventory strategies that reduce waste by 15% while maintaining product availability.

**Supported by:**
- product.csv (product_id, is_perishable, shelf_life_days, category, sub_category)
- item.csv (item_id, product_id, expiry_date)
- invoice_line.csv (item_id, quantity, invoiceDate)

## Scenario 4: Product Pricing and Margin Optimization

**Business Context:**
Sobeys needs to optimize its pricing strategy to balance competitiveness with profitability. The data includes detailed cost, price, and margin information.

**User Story:**
> As the VP of Advanced Analytics at Sobeys, I need to analyze the relationship between product pricing, sales volume, and margins, so that we can implement an optimized pricing strategy that increases overall profitability by 4%.

**Supported by:**
- item.csv (item_id, product_id)
- cost.csv (item_id, selling_cost, purchase_cost, margin_percentage)
- invoice_line.csv (item_id, quantity, unit_price, line_total_amount)


## Scenario 5: Recent Customer Activity Tracking

**Business Context:**
Sobeys needs to identify active versus inactive customers to target re-engagement campaigns and optimize marketing spend. The business wants to specifically track which customers have made purchases in the last quarter.

**User Story:**
> As the VP of Advanced Analytics at Sobeys, I need to identify customers who have made purchases in the last quarter, so that we can develop targeted re-engagement campaigns for inactive customers and increase our customer retention rate by 8%.

**Supported by:**
- customer.csv (customer_id, customer_type, loyalty_tier)
- invoice.csv (invoice_id, created_timestamp)
- invoice_line.csv (invoice_id, customer_id, line_total_amount)