# Databricks notebook source
# MAGIC %md
# MAGIC # Silver Layer Utility Functions
# MAGIC
# MAGIC This notebook contains utility functions for the silver layer processing, including:
# MAGIC - Data type conversion
# MAGIC - Column standardization
# MAGIC - Data quality validation
# MAGIC - Delta table operations (upsert, create external table)
# MAGIC - Key validation
# MAGIC
# MAGIC These functions are designed to be reused across all silver layer notebooks.

# COMMAND ----------

# MAGIC %run ../start_up

# COMMAND ----------

# Run the startup notebook to get access to common utilities and configurations
from pyspark.sql.functions import (
    col, current_timestamp, lit, when, upper, lower, trim, regexp_replace,
    to_timestamp, to_date, datediff, year, month, dayofmonth, dayofweek,
    hour, minute, second, expr, concat, concat_ws, coalesce, regexp_extract,
    isnan, isnull, length, substring
)
from pyspark.sql.types import (
    StringType, IntegerType, LongType, FloatType, DoubleType, 
    BooleanType, TimestampType, DateType, DecimalType
)
from pyspark.sql.utils import AnalysisException
from delta.tables import DeltaTable
import re

# COMMAND ----------

# Initialize logger using log_path from config
# component_log_levels is imported from start_up notebook
silver_utils_logger = create_logger(component_log_levels=component_log_levels)
silver_utils_logger.info("Initializing silver utilities")

# COMMAND ----------

# MAGIC %md
# MAGIC ## Column Mapping Functions

# COMMAND ----------

@log_execution(silver_utils_logger)
def apply_column_mapping(df, column_mapping):
    """Apply column mapping to rename columns from source to target names.
    
    Args:
        df: Source DataFrame
        column_mapping: Dictionary mapping source column names to target column names
        
    Returns:
        DataFrame with renamed columns
    """
    silver_utils_logger.info(f"Applying column mapping: {column_mapping}")
    
    # Create a new DataFrame with mapped column names
    for source_col, target_col in column_mapping.items():
        if source_col in df.columns:
            df = df.withColumnRenamed(source_col, target_col)
    
    return df

# COMMAND ----------

# MAGIC %md
# MAGIC ## Data Type Conversion Functions

# COMMAND ----------

@log_execution(silver_utils_logger)
def convert_data_types(df, type_mappings):
    """Convert columns to specified data types.
    
    Args:
        df: Source DataFrame
        type_mappings: Dictionary mapping column names to data types
            Example: {"customer_id": StringType(), "age": IntegerType()}
        
    Returns:
        DataFrame with converted data types
    """
    silver_utils_logger.info(f"Converting data types for columns: {list(type_mappings.keys())}")
    
    for column, data_type in type_mappings.items():
        if column in df.columns:
            df = df.withColumn(column, col(column).cast(data_type))
    
    return df

@log_execution(silver_utils_logger)
def convert_timestamp_columns(df, timestamp_columns, format_str="M/d/yyyy"):
    """Convert string columns to timestamp type.
    
    Args:
        df: Source DataFrame
        timestamp_columns: List of column names to convert to timestamp
        format_str: Date format string (default: "M/d/yyyy")
        
    Returns:
        DataFrame with timestamp columns
    """
    silver_utils_logger.info(f"Converting timestamp columns: {timestamp_columns}")
    
    for ts_col in timestamp_columns:
        if ts_col in df.columns:
            df = df.withColumn(ts_col, to_timestamp(col(ts_col), format_str))
    
    return df

@log_execution(silver_utils_logger)
def convert_boolean_columns(df, boolean_columns):
    """Convert columns to boolean type.
    
    Args:
        df: Source DataFrame
        boolean_columns: List of column names to convert to boolean
        
    Returns:
        DataFrame with boolean columns
    """
    silver_utils_logger.info(f"Converting boolean columns: {boolean_columns}")
    
    for bool_col in boolean_columns:
        if bool_col in df.columns:
            # Handle various boolean representations
            df = df.withColumn(
                bool_col,
                when(col(bool_col).isin(["true", "True", "TRUE", "1", "t", "y", "yes", "Y", "Yes"]), True)
                .when(col(bool_col).isin(["false", "False", "FALSE", "0", "f", "n", "no", "N", "No"]), False)
                .otherwise(None)  # Use NULL for values that don't match any pattern
                .cast(BooleanType())
            )
    
    return df

@log_execution(silver_utils_logger)
def convert_numeric_columns(df, numeric_columns, data_type=DoubleType()):
    """Convert columns to numeric type.
    
    Args:
        df: Source DataFrame
        numeric_columns: List of column names to convert to numeric
        data_type: Numeric data type to convert to (default: DoubleType)
        
    Returns:
        DataFrame with numeric columns
    """
    silver_utils_logger.info(f"Converting numeric columns: {numeric_columns}")
    
    for num_col in numeric_columns:
        if num_col in df.columns:
            # Clean numeric strings (remove currency symbols, commas, etc.)
            df = df.withColumn(
                num_col,
                regexp_replace(col(num_col), "[^-0-9.]", "")
            )
            df = df.withColumn(num_col, col(num_col).cast(data_type))
    
    return df

# COMMAND ----------

# MAGIC %md
# MAGIC ## Column Standardization Functions

# COMMAND ----------

@log_execution(silver_utils_logger)
def standardize_text_columns(df, text_columns, case="upper"):
    """Standardize text columns by applying case conversion and trimming.
    
    Args:
        df: Source DataFrame
        text_columns: List of column names to standardize
        case: Case conversion ("upper", "lower", or "title")
        
    Returns:
        DataFrame with standardized text columns
    """
    silver_utils_logger.info(f"Standardizing text columns: {text_columns}")
    
    for text_col in text_columns:
        if text_col in df.columns:
            # Trim whitespace
            df = df.withColumn(text_col, trim(col(text_col)))
            
            # Apply case conversion
            if case.lower() == "upper":
                df = df.withColumn(text_col, upper(col(text_col)))
            elif case.lower() == "lower":
                df = df.withColumn(text_col, lower(col(text_col)))
            elif case.lower() == "title":
                # Simple title case implementation
                df = df.withColumn(
                    text_col,
                    expr(f"initcap({text_col})")
                )
    
    return df

@log_execution(silver_utils_logger)
def standardize_phone_numbers(df, phone_columns):
    """Standardize phone number columns to format: XXX-XXX-XXXX.
    
    Args:
        df: Source DataFrame
        phone_columns: List of column names containing phone numbers
        
    Returns:
        DataFrame with standardized phone numbers
    """
    silver_utils_logger.info(f"Standardizing phone columns: {phone_columns}")
    
    for phone_col in phone_columns:
        if phone_col in df.columns:
            # Extract digits only
            df = df.withColumn(
                phone_col + "_digits",
                regexp_replace(col(phone_col), "[^0-9]", "")
            )
            
            # Format as XXX-XXX-XXXX (assuming 10-digit North American numbers)
            df = df.withColumn(
                phone_col,
                when(
                    length(col(phone_col + "_digits")) == 10,
                    concat(
                        substring(col(phone_col + "_digits"), 1, 3),
                        lit("-"),
                        substring(col(phone_col + "_digits"), 4, 3),
                        lit("-"),
                        substring(col(phone_col + "_digits"), 7, 4)
                    )
                ).otherwise(col(phone_col))
            )
            
            # Drop temporary column
            df = df.drop(phone_col + "_digits")
    
    return df

@log_execution(silver_utils_logger)
def standardize_categorical_values(df, column, valid_values, default_value=None):
    """Standardize categorical values to ensure they are within a valid set.
    
    Args:
        df: Source DataFrame
        column: Column name to standardize
        valid_values: List of valid values for the column
        default_value: Default value to use for invalid values (default: None)
        
    Returns:
        DataFrame with standardized categorical values
    """
    silver_utils_logger.info(f"Standardizing categorical column: {column}")
    
    if column in df.columns:
        # Convert to uppercase for case-insensitive comparison
        df = df.withColumn(column, upper(col(column)))
        
        # Create validation column
        df = df.withColumn(
            column + "_valid",
            col(column).isin([v.upper() for v in valid_values])
        )
        
        # Replace invalid values with default if specified
        if default_value is not None:
            df = df.withColumn(
                column,
                when(col(column + "_valid"), col(column)).otherwise(lit(default_value))
            )
    
    return df

# COMMAND ----------

# MAGIC %md
# MAGIC ## Data Quality Functions

# COMMAND ----------

@log_execution(silver_utils_logger)
def validate_not_null(df, columns):
    """Validate that columns are not null.
    
    Args:
        df: Source DataFrame
        columns: List of column names to validate
        
    Returns:
        DataFrame with validation columns added
    """
    silver_utils_logger.info(f"Validating not null columns: {columns}")
    
    for col_name in columns:
        if col_name in df.columns:
            df = df.withColumn(
                col_name + "_not_null",
                ~(isnull(col(col_name)) | (col(col_name) == ""))
            )
    
    return df

@log_execution(silver_utils_logger)
def validate_unique(df, columns):
    """Validate that columns have unique values.
    
    Args:
        df: Source DataFrame
        columns: List of column names to validate
        
    Returns:
        DataFrame with validation columns added
    """
    silver_utils_logger.info(f"Validating unique columns: {columns}")
    
    for col_name in columns:
        if col_name in df.columns:
            # Count occurrences of each value
            value_counts = df.groupBy(col_name).count()
            
            # Join back to original DataFrame
            df = df.join(
                value_counts.withColumnRenamed("count", col_name + "_count"),
                on=col_name,
                how="left"
            )
            
            # Add validation column
            df = df.withColumn(
                col_name + "_unique",
                col(col_name + "_count") == 1
            )
            
            # Drop temporary column
            df = df.drop(col_name + "_count")
    
    return df

@log_execution(silver_utils_logger)
def validate_regex_pattern(df, column, pattern, validation_name=None):
    """Validate that column values match a regex pattern.
    
    Args:
        df: Source DataFrame
        column: Column name to validate
        pattern: Regex pattern to match
        validation_name: Name for the validation column (default: column + "_valid")
        
    Returns:
        DataFrame with validation column added
    """
    silver_utils_logger.info(f"Validating regex pattern for column: {column}")
    
    if column in df.columns:
        validation_col = validation_name if validation_name else column + "_valid"
        
        df = df.withColumn(
            validation_col,
            col(column).rlike(pattern)
        )
    
    return df

@log_execution(silver_utils_logger)
def calculate_data_quality_score(df):
    """Calculate data quality score based on validation columns.
    
    Args:
        df: Source DataFrame with validation columns (ending with _valid, _not_null, _unique)
        
    Returns:
        DataFrame with data_quality_score column added
    """
    silver_utils_logger.info("Calculating data quality score")
    
    # Find all validation columns
    validation_columns = [c for c in df.columns if c.endswith("_valid") or c.endswith("_not_null") or c.endswith("_unique")]
    
    if validation_columns:
        # Calculate quality score as percentage of passing validations
        quality_expr = " + ".join([f"CASE WHEN {c} THEN 1 ELSE 0 END" for c in validation_columns])
        df = df.withColumn(
            "data_quality_score",
            expr(f"({quality_expr}) * 100.0 / {len(validation_columns)}")
        )
    else:
        df = df.withColumn("data_quality_score", lit(100.0))
    
    return df

# COMMAND ----------

# MAGIC %md
# MAGIC ## Delta Table Operations

# COMMAND ----------

@log_execution(silver_utils_logger)
def create_external_table(spark, catalog, schema, table_name, location, format="delta"):
    """Create an external table in Unity Catalog.
    
    Args:
        spark: Spark session
        catalog: Catalog name
        schema: Schema name
        table_name: Table name
        location: Table location
        format: Table format (default: "delta")
        
    Returns:
        True if table was created, False if it already exists
    """
    silver_utils_logger.info(f"Creating external table {catalog}.{schema}.{table_name}")
    
    try:
        # Check if table exists
        spark.sql(f"DESCRIBE TABLE {catalog}.{schema}.{table_name}")
        silver_utils_logger.info(f"Table {catalog}.{schema}.{table_name} already exists")
        return False
    except AnalysisException:
        # Create external table
        spark.sql(f"""
        CREATE EXTERNAL TABLE IF NOT EXISTS {catalog}.{schema}.{table_name}
        USING {format}
        LOCATION '{location}'
        """)
        silver_utils_logger.info(f"Created external table {catalog}.{schema}.{table_name}")
        return True

@log_execution(silver_utils_logger)
def upsert_to_delta_table(spark, source_df, target_path, merge_condition, catalog=None, schema=None, table_name=None):
    """Upsert data to a Delta table using merge operation.
    
    Args:
        spark: Spark session
        source_df: Source DataFrame
        target_path: Path to target Delta table
        merge_condition: Merge condition (e.g., "target.id = source.id")
        catalog: Optional catalog name
        schema: Optional schema name
        table_name: Optional table name
        
    Returns:
        True if upsert was successful
    """
    silver_utils_logger.info(f"Upserting data to {target_path}")
    
    try:
        # Try to load the target Delta table
        delta_table = DeltaTable.forPath(spark, target_path)
        table_exists = True
    except AnalysisException:
        # Table doesn't exist, create it
        table_exists = False
    
    if table_exists:
        # Perform merge operation
        silver_utils_logger.info(f"Merging data with condition: {merge_condition}")
        delta_table.alias("target").merge(
            source_df.alias("source"),
            merge_condition
        ).whenMatchedUpdateAll().whenNotMatchedInsertAll().execute()
    else:
        # Write data to new table
        silver_utils_logger.info(f"Creating new Delta table at {target_path}")
        if catalog and schema and table_name:
            # Use fully qualified table name for Unity Catalog
            full_table_name = f"{catalog}.{schema}.{table_name}"
            silver_utils_logger.info(f"Registering table in Unity Catalog as {full_table_name}")
            source_df.write.format("delta").mode("overwrite").option("path", target_path).saveAsTable(full_table_name)
        else:
            # Just write to the path without registering in Unity Catalog
            silver_utils_logger.info(f"Writing to Delta path without Unity Catalog registration")
            source_df.write.format("delta").mode("overwrite").save(target_path)
    
    return True

@log_execution(silver_utils_logger)
def validate_primary_key(df, primary_key):
    """Validate that primary key column(s) have unique, non-null values.
    
    Args:
        df: Source DataFrame
        primary_key: Primary key column name or list of column names for composite key
        
    Returns:
        Tuple of (DataFrame with validation columns, is_valid boolean)
    """
    silver_utils_logger.info(f"Validating primary key: {primary_key}")
    
    # Convert single column to list
    if isinstance(primary_key, str):
        primary_key = [primary_key]
    
    # Validate not null
    df = validate_not_null(df, primary_key)
    
    # Check for uniqueness of the key combination
    key_counts = df.groupBy(*primary_key).count()
    
    # Join back to original DataFrame
    df = df.join(
        key_counts.withColumnRenamed("count", "pk_count"),
        on=primary_key,
        how="left"
    )
    
    # Add validation column
    pk_col_name = "_".join(primary_key) + "_pk_valid"
    df = df.withColumn(pk_col_name, col("pk_count") == 1)
    
    # Check if all primary key values are valid
    pk_valid = df.filter(~col(pk_col_name)).count() == 0
    
    # Drop temporary column
    df = df.drop("pk_count")
    
    return df, pk_valid

# COMMAND ----------

# MAGIC %md
# MAGIC ## Data Read/Write Functions

# COMMAND ----------

@log_execution(silver_utils_logger)
def read_bronze_data(spark, bronze_path, table_name, date=None, logger=None):
    """Read data from bronze layer.
    
    Args:
        spark: Spark session
        bronze_path: Path to bronze layer
        table_name: Table name to read
        date: Optional date partition to read (default: None, reads all partitions)
        logger: Optional logger to use (default: silver_utils_logger)
        
    Returns:
        DataFrame with bronze data
    """
    log = logger if logger else silver_utils_logger
    log.info(f"Reading bronze data for table: {table_name}, date: {date if date else 'all'}") 
    
    if date:
        # Read specific partition
        df = spark.read.option("mergeSchema", "true").parquet(f"{bronze_path}/{table_name}/{date.replace('-', '/')}")
    else:
        # Read all partitions with schema merging
        df = spark.read.option("mergeSchema", "true").parquet(f"{bronze_path}/{table_name}")
    
    if logger:
        return log_dataframe_info(df, f"{table_name}_bronze", logger)
    return df

@log_execution(silver_utils_logger)
def write_silver_data(spark, df, silver_path, table_name, primary_key, column_mapping=None, catalog=None, schema=None, format="delta", logger=None):
    """Write data to silver layer using Delta format.
    
    Args:
        spark: Spark session
        df: DataFrame to write
        silver_path: Path to silver layer
        table_name: Table name to write
        primary_key: Primary key column name or list of column names
        column_mapping: Optional dictionary mapping source column names to target column names
        catalog: Optional catalog name
        schema: Optional schema name
        format: Table format (default: "delta")
        logger: Optional logger to use (default: silver_utils_logger)
        
    Returns:
        DataFrame that was written
    """
    log = logger if logger else silver_utils_logger
    log.info(f"Writing data to silver layer for table: {table_name}") 
    
    target_path = f"{silver_path}/{table_name}"
    
    # Apply column mapping if provided
    if column_mapping:
        log.info(f"Applying column mapping before merge: {column_mapping}")
        df = apply_column_mapping(df, column_mapping)
    
    # Convert primary_key to string if it's a list
    if isinstance(primary_key, list):
        merge_condition = " AND ".join([f"target.{pk} = source.{pk}" for pk in primary_key])
    else:
        merge_condition = f"target.{primary_key} = source.{primary_key}"
    
    # Use the upsert utility function
    upsert_to_delta_table(
        spark=spark,
        source_df=df,
        target_path=target_path,
        merge_condition=merge_condition,
        catalog=catalog,
        schema=schema,
        table_name=table_name
    )
    
    if logger:
        return log_dataframe_info(df, f"{table_name}_silver", logger)
    return df