# Databricks notebook source
# MAGIC %md
# MAGIC # Site - Bronze to Silver Transformation
# MAGIC
# MAGIC This notebook processes site data from the bronze layer to the silver layer. It applies data type conversions, handles timestamp formatting, standardizes values, and performs data quality checks.

# COMMAND ----------

# MAGIC %run ../../start_up

# COMMAND ----------

from pyspark.sql.functions import (
    input_file_name, current_timestamp, lit, col, to_timestamp, when, 
    upper, regexp_extract, datediff, expr, split
)
from pyspark.sql.utils import AnalysisException
from delta.tables import DeltaTable

# Use the logger configuration from startup
site_silver_logger = create_logger(component_log_levels=component_log_levels)
site_silver_logger.info("Initializing notebook")
 
# Extract frequently used config values into variables
catalog = pipeline_config["catalog"]
target_date = pipeline_config["default_processing_date"]
bronze_schema = pipeline_config["schemas"]["bronze"]
silver_schema = pipeline_config["schemas"]["silver"]
bronze_path = pipeline_config["paths"]["bronze_path"]
silver_path = pipeline_config["paths"]["silver_path"]
silver_format = pipeline_config["file_formats"]["silver"]
delta_properties = pipeline_config["delta_properties"]

# Switch to catalog
spark.sql(f"USE CATALOG {catalog}")

# COMMAND ----------

# Table metadata for site table
table_metadata = {
    "primary_key": "site_id",
    "timestamp_columns": ["opening_date", "closing_date", "created_timestamp", "modified_timestamp"],
    "numeric_columns": ["store_size_sqft"],
    "boolean_columns": ["is_active"],
    "store_types": ["Supermarket", "Hypermarket", "Convenience", "Discount", "Specialty", "Warehouse", "Express"],
    "store_formats": ["Urban", "Suburban", "Rural", "Mall", "Standalone", "Strip Mall"]
}

# COMMAND ----------

# Function to read data from bronze layer
@log_execution(site_silver_logger)
def read_bronze_data(date=None):
    site_silver_logger.info(f"Reading bronze data for table: site, date: {date if date else 'all'}") 
    if date:
        # Read specific partition
        df = spark.read.option("mergeSchema", "true").parquet(f"{bronze_path}/site/ingestion_date={date}")
    else:
        # Read all partitions with schema merging
        df = spark.read.option("mergeSchema", "true").parquet(f"{bronze_path}/site")
    
    return log_dataframe_info(df, "site_bronze", site_silver_logger)

# Function to apply transformations based on table metadata
@log_execution(site_silver_logger)
def apply_transformations(df):
    site_silver_logger.info("Applying transformations to site data")
    
    # Apply timestamp conversions
    for ts_col in table_metadata["timestamp_columns"]:
        if ts_col in df.columns:
            df = df.withColumn(ts_col, to_timestamp(col(ts_col), "M/d/yyyy"))
    
    # Apply numeric conversions
    for num_col in table_metadata["numeric_columns"]:
        if num_col in df.columns:
            df = df.withColumn(num_col, col(num_col).cast("double"))
    
    # Apply boolean conversions
    for bool_col in table_metadata["boolean_columns"]:
        if bool_col in df.columns:
            df = df.withColumn(bool_col, col(bool_col).cast("boolean"))
    
    # Validate store_size_sqft > 0
    if "store_size_sqft" in df.columns:
        df = df.withColumn(
            "store_size_valid", 
            col("store_size_sqft") > 0
        )
    
    # Validate email format
    if "email" in df.columns:
        df = df.withColumn(
            "email_valid", 
            when(col("email").rlike("^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,6}$"), True).otherwise(False)
        )
    
    # Validate phone format
    if "phone" in df.columns:
        df = df.withColumn(
            "phone_valid", 
            when(col("phone").rlike("^[0-9]{3}-[0-9]{3}-[0-9]{4}$"), True).otherwise(False)
        )
    
    # Standardize store_type
    if "store_type" in df.columns:
        df = df.withColumn("store_type", upper(col("store_type")))
        df = df.withColumn(
            "store_type_valid", 
            col("store_type").isin([type.upper() for type in table_metadata["store_types"]])
        )
    
    # Standardize store_format
    if "store_format" in df.columns:
        df = df.withColumn("store_format", upper(col("store_format")))
        df = df.withColumn(
            "store_format_valid", 
            col("store_format").isin([format.upper() for format in table_metadata["store_formats"]])
        )
    
    # Extract address components
    if "address" in df.columns:
        # Extract postal code (assuming Canadian format: A1A 1A1)
        df = df.withColumn(
            "postal_code", 
            regexp_extract(col("address"), "[A-Z][0-9][A-Z]\\s?[0-9][A-Z][0-9]", 0)
        )
        
        # Extract province (assuming Canadian province codes)
        df = df.withColumn(
            "province", 
            regexp_extract(col("address"), "\\b(AB|BC|MB|NB|NL|NS|NT|NU|ON|PE|QC|SK|YT)\\b", 0)
        )
        
        # Extract city (assuming format: "123 Main St, City, Province A1A 1A1")
        df = df.withColumn(
            "city", 
            regexp_extract(col("address"), ", ([^,]+),", 1)
        )
        
        # Extract street address
        df = df.withColumn(
            "street_address", 
            regexp_extract(col("address"), "^([^,]+),", 1)
        )
    
    # Parse operating hours
    if "operating_hours" in df.columns:
        # Assuming format like "Mon-Fri: 9AM-9PM, Sat-Sun: 10AM-6PM"
        df = df.withColumn(
            "weekday_hours", 
            regexp_extract(col("operating_hours"), "Mon-Fri:\\s*([^,]+)", 1)
        )
        
        df = df.withColumn(
            "weekend_hours", 
            regexp_extract(col("operating_hours"), "Sat-Sun:\\s*([^,]+)", 1)
        )
    
    # Calculate site age in days
    if "opening_date" in df.columns:
        df = df.withColumn(
            "site_age_days",
            datediff(current_timestamp(), col("opening_date"))
        )
    
    # Add data quality score
    quality_columns = [col for col in df.columns if col.endswith("_valid")]
    if quality_columns:
        # Calculate quality score as percentage of passing validations
        quality_expr = " + ".join([f"CASE WHEN {col} THEN 1 ELSE 0 END" for col in quality_columns])
        df = df.withColumn(
            "data_quality_score",
            expr(f"({quality_expr}) * 100.0 / {len(quality_columns)}")
        )
    else:
        df = df.withColumn("data_quality_score", lit(100.0))
    
    # Add processing timestamp
    df = df.withColumn("processed_at", current_timestamp())
    
    return log_dataframe_info(df, "site_transformed", site_silver_logger)

# Function to write data to silver layer
@log_execution(site_silver_logger)
def write_to_silver(df):
    site_silver_logger.info("Writing site data to silver layer")
    table_name = "site"
    target_path = f"{silver_path}/{table_name}"
    
    # Create or replace the table in Unity Catalog
    try:
        # Check if table exists
        spark.sql(f"DESCRIBE TABLE {catalog}.{silver_schema}.{table_name}")
        table_exists = True
    except AnalysisException:
        table_exists = False
    
    # Write data using Delta format
    if table_exists:
        # Merge data using primary key
        site_silver_logger.info(f"Merging data into existing table {catalog}.{silver_schema}.{table_name}")
        delta_table = DeltaTable.forName(spark, f"{catalog}.{silver_schema}.{table_name}")
        
        # Merge condition based on primary key
        merge_condition = f"target.{table_metadata['primary_key']} = source.{table_metadata['primary_key']}"
        
        # Perform merge operation
        delta_table.alias("target").merge(
            df.alias("source"),
            merge_condition
        ).whenMatchedUpdateAll().whenNotMatchedInsertAll().execute()
    else:
        # Create new table
        site_silver_logger.info(f"Creating new table {catalog}.{silver_schema}.{table_name}")
        df.write \
            .format(silver_format) \
            .option("mergeSchema", "true") \
            .mode("overwrite") \
            .saveAsTable(f"{catalog}.{silver_schema}.{table_name}")
    
    return df

# Main processing function
@log_execution(site_silver_logger)
def process_bronze_to_silver(target_date=None):
    site_silver_logger.info(f"Processing site data from bronze to silver for date: {target_date if target_date else 'all'}")
    
    # Read bronze data
    bronze_df = read_bronze_data(target_date)
    if bronze_df is None or bronze_df.isEmpty():
        site_silver_logger.warning(f"No bronze data found for site on date {target_date}")
        return None
    
    # Apply transformations
    silver_df = apply_transformations(bronze_df)
    
    # Write to silver layer
    final_df = write_to_silver(silver_df)
    
    site_silver_logger.info("Completed bronze to silver processing for site")
    return final_df

# COMMAND ----------

# Process site data
silver_df = process_bronze_to_silver(target_date)
if silver_df is not None:
    log_dataframe_info(silver_df, "site_silver_final", site_silver_logger)
    display(silver_df)